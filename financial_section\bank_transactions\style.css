/* ===== تنسيق صفحة المعاملات البنكية ===== */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap');

:root {
    /* ألوان مشتركة */
    --light-color: #f5f7fa;
    --border-color: #e1e8ed;
    --text-dark: #2c3e50;
    --text-muted: #7f8c8d;
    --text-light: #ffffff;
    --danger-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --info-color: #3498db;
    
    /* تعريف متغيرات الألوان للتبديل بين المركزي والآجل */
    /* الخطأ الأولى هي قيم افتراضية وسيتم تغييرها ديناميكيًا */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --accent-color: #1abc9c;
    --gradient-start: #3498db;
    --gradient-end: #2c3e50;
    --card-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
    --card-bg: #ffffff;
    --table-header-bg: rgba(52, 152, 219, 0.08);
    --table-row-hover: rgba(52, 152, 219, 0.05);
    --positive-amount-color: #27ae60;
    --negative-amount-color: #e74c3c;
}

/* تطبيق ألوان البنوك المركزية */
.bank-type-markazi {
    --primary-color: #1e3a8a;
    --primary-dark: #172554;
    --secondary-color: #374151;
    --accent-color: #3b82f6;
    --gradient-start: #1e3a8a;
    --gradient-end: #172554;
    --table-header-bg: rgba(30, 58, 138, 0.08);
    --table-row-hover: rgba(30, 58, 138, 0.05);
}

/* تطبيق ألوان البنوك الآجلة */
.bank-type-ajel {
    --primary-color: #9f1239;
    --primary-dark: #7f1d1d;
    --secondary-color: #4b5563;
    --accent-color: #f43f5e;
    --gradient-start: #9f1239;
    --gradient-end: #7f1d1d;
    --table-header-bg: rgba(159, 18, 57, 0.08);
    --table-row-hover: rgba(159, 18, 57, 0.05);
}

/* إعدادات عامة */
body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--light-color);
    color: var(--text-dark);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* Dashboard Layout */
.dashboard-layout {
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    font-family: 'Tajawal', sans-serif;
    overflow-x: hidden;
}

/* Top Navigation Bar */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

.navbar-brand i {
    font-size: 1.3rem;
}

.navbar-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logout-btn {
    background-color: rgba(255, 255, 255, 0.15);
    color: var(--text-light);
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.25);
}

/* Main Content */
.main-content {
    margin-top: 70px;
    padding: 0;
    min-height: calc(100vh - 70px);
    transition: margin-right 0.3s ease;
}

.content-wrapper {
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    gap: 30px;
}

/* تنسيقات قسم البنوك الجانبي */
.banks-sidebar-section {
    position: sticky;
    top: 20px;
    height: fit-content;
    width: 300px;
    flex-shrink: 0;
}

/* تنسيقات المحتوى الرئيسي */
.main-dashboard-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.banks-sidebar-card {
    background-color: var(--card-bg);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 140px);
}

.banks-sidebar-card .sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    background-color: #f8f9fa;
}

.banks-sidebar-card .sidebar-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.3rem;
}

.banks-sidebar-card .sidebar-list {
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-y: auto;
    flex: 1;
    max-height: 400px;
}

.banks-sidebar-card .sidebar-list li {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.banks-sidebar-card .sidebar-list li:last-child {
    border-bottom: none;
}

.banks-sidebar-card .sidebar-list a {
    display: flex;
    padding: 15px 20px;
    color: var(--text-dark);
    text-decoration: none;
    transition: all 0.3s ease;
}

.banks-sidebar-card .sidebar-list a:hover {
    background-color: var(--table-row-hover);
}

.banks-sidebar-card .sidebar-list a.active {
    background-color: var(--table-header-bg);
    color: var(--primary-color);
    font-weight: 600;
    border-right: 4px solid var(--primary-color);
}

/* تنسيق خاص لخيار "الكل" */
.banks-sidebar-card .sidebar-list a.all-banks-option {
    font-weight: bold;
    background-color: var(--secondary-color);
    color: var(--text-light);
    border: 2px solid var(--primary-color);
    margin-bottom: 10px;
    border-radius: 8px;
}

.banks-sidebar-card .sidebar-list a.all-banks-option:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--accent-color);
}

.banks-sidebar-card .sidebar-list a.all-banks-option.active {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
    border-right: 4px solid var(--accent-color);
}

.banks-sidebar-card .sidebar-list a i {
    margin-left: 10px;
    color: var(--primary-color);
}

.banks-sidebar-card .sidebar-footer {
    padding: 15px;
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.banks-sidebar-card .sidebar-btn {
    background-color: var(--secondary-color);
    color: var(--text-light);
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.banks-sidebar-card .sidebar-btn:hover {
    background-color: var(--primary-dark);
}

/* Add Bank Button in Sidebar */
.banks-sidebar-card .sidebar-btn.add-btn {
    background-color: var(--success-color, #2ecc71);
    color: var(--text-light, #ffffff);
}

.banks-sidebar-card .sidebar-btn.add-btn:hover {
    background-color: #27ae60;
}

/* Switch Bank Type Button */
.banks-sidebar-card .sidebar-btn.switch-btn {
    background-color: var(--info-color, #3498db);
    color: var(--text-light, #ffffff);
    font-weight: 600;
}

.banks-sidebar-card .sidebar-btn.switch-btn:hover {
    background-color: #2980b9;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

/* Bank Type Specific Colors for Switch Button */
.bank-type-markazi .banks-sidebar-card .sidebar-btn.switch-btn {
    background-color: #e74c3c; /* أحمر للتبديل إلى الآجل */
}

.bank-type-markazi .banks-sidebar-card .sidebar-btn.switch-btn:hover {
    background-color: #c0392b;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.bank-type-ajel .banks-sidebar-card .sidebar-btn.switch-btn {
    background-color: var(--primary-color, #3498db); /* أزرق للتبديل إلى المركزي */
}

.bank-type-ajel .banks-sidebar-card .sidebar-btn.switch-btn:hover {
    background-color: var(--primary-dark, #2980b9);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.banks-sidebar-card .loading-message {
    text-align: center;
    color: var(--text-muted);
    padding: 15px;
    font-style: italic;
}

/* تنسيقات المحتوى الرئيسي */
.dashboard-page-header {
    margin-bottom: 25px;
}

.dashboard-page-header h1 {
    color: var(--primary-color);
    margin: 0 0 8px 0;
    font-size: 1.8rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.dashboard-page-header p {
    color: var(--text-muted);
    margin: 0;
    font-size: 1.1rem;
}

.message {
    padding: 12px 15px;
    border-radius: 8px;
    margin: 15px 0;
    font-weight: 500;
    display: none;
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.message.warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(243, 156, 18, 0.2);
}

/* تنسيقات البطاقات */
.filter-card, .table-card {
    background-color: var(--card-bg);
    border-radius: 12px;
    margin-bottom: 25px;
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-body {
    padding: 20px;
}

/* تنسيقات التصفية */
.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    grid-gap: 15px;
}

.filter-actions {
    grid-column: 1 / -1;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 10px;
}

.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--secondary-color);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: 'Tajawal', sans-serif;
    color: var(--text-dark);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);
}

.search-group {
    grid-column: span 2;
}

.filter-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.apply-filters-btn {
    background-color: var(--primary-color);
    color: white;
}

.apply-filters-btn:hover {
    background-color: var(--primary-dark);
}

.reset-filters-btn {
    background-color: #f0f0f0;
    color: var(--text-dark);
}

.reset-filters-btn:hover {
    background-color: #e0e0e0;
}

/* تنسيقات الجدول */
.table-responsive {
    overflow-x: auto;
}

#transactions-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 5px;
}

#transactions-table th,
#transactions-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

#transactions-table th {
    background-color: var(--table-header-bg);
    color: var(--primary-color);
    font-weight: 600;
}

#transactions-table tr:last-child td {
    border-bottom: none;
}

#transactions-table tr:hover {
    background-color: var(--table-row-hover);
}

.badge {
    background-color: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 30px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* تنسيقات مبالغ المعاملات */
.amount-positive {
    color: var(--positive-amount-color);
    font-weight: 600;
}

.amount-negative {
    color: var(--negative-amount-color);
    font-weight: 600;
}

/* تنسيقات أزرار الإجراءات في الجدول */
.action-btn {
    background-color: transparent;
    border: none;
    font-size: 1rem;
    padding: 5px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.edit-btn {
    color: var(--primary-color);
}

.edit-btn:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.delete-btn {
    color: var(--danger-color);
}

.delete-btn:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* تنسيقات ترقيم الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
}

.pagination button {
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    border: none;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination button:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* تنسيقات أزرار الإضافة والتحكم */
.control-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.control-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.add-btn {
    background-color: var(--success-color);
}

/* تنسيقات النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    animation: modalFadeIn 0.3s ease-out;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content.large {
    max-width: 800px;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.close-modal-btn {
    position: absolute;
    left: 20px;
    top: 15px;
    font-size: 24px;
    color: var(--text-muted);
    background: none;
    border: none;
    cursor: pointer;
}

.close-modal-btn:hover {
    color: var(--danger-color);
}

.modal-content h2 {
    margin: 0;
    color: var(--primary-color);
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.modal .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
}

.modal .form-group-full-width {
    grid-column: 1 / -1;
}

.modal input,
.modal select,
.modal textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: 'Tajawal', sans-serif;
}

.modal input:focus,
.modal select:focus,
.modal textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);
}

.required {
    color: var(--danger-color);
}

.modal .form-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    padding: 0 20px 20px;
}

.submit-btn, .cancel-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    min-width: 120px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.submit-btn {
    background-color: var(--primary-color);
    color: white;
}

.submit-btn:hover {
    background-color: var(--primary-dark);
}

.cancel-btn {
    background-color: #f0f0f0;
    color: var(--text-dark);
}

.cancel-btn:hover {
    background-color: #e0e0e0;
}

/* أنماط أيقونة المعلومات */
.tooltip-icon {
    cursor: help;
    color: var(--primary-color);
    margin-right: 5px;
}

/* تذييل الصفحة */
.main-footer {
    text-align: center;
    padding: 15px;
    background-color: var(--card-bg);
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* تحسينات للجوال والأجهزة اللوحية */
@media (max-width: 1200px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .banks-sidebar-section {
        width: 280px;
    }

    .content-wrapper {
        gap: 20px;
    }
}

@media (max-width: 992px) {
    .content-wrapper {
        flex-direction: column;
        gap: 20px;
    }

    .banks-sidebar-section {
        position: static;
        width: 100%;
        order: -1;
    }

    .banks-sidebar-card {
        max-height: 300px;
    }
}

@media (max-width: 768px) {
    .navbar-brand span {
        display: none;
    }

    .content-wrapper {
        padding: 20px 15px;
        flex-direction: column;
    }

    .filter-grid {
        grid-template-columns: 1fr;
    }

    .search-group {
        grid-column: 1;
    }

    .modal-content {
        width: 95%;
    }

    .modal .form-grid {
        grid-template-columns: 1fr;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .card-header > div {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    #transactions-table {
        font-size: 0.9rem;
    }

    #transactions-table th,
    #transactions-table td {
        padding: 8px 10px;
    }
}

@media (max-width: 576px) {
    .filter-actions {
        flex-direction: column;
    }
    
    .filter-actions button {
        width: 100%;
    }
    
    .modal .form-actions {
        flex-direction: column;
    }
    
    .submit-btn, .cancel-btn {
        width: 100%;
    }
    
    .pagination button {
        min-width: 35px;
        height: 35px;
        padding: 0 8px;
        font-size: 0.9rem;
    }
}

/* تعريف متغيرات RGB للاستخدام في التعتيم والشفافية */
:root {
    --primary-color-rgb: 52, 152, 219; /* الافتراضي */
}

.bank-type-markazi {
    --primary-color-rgb: 30, 58, 138;
}

.bank-type-ajel {
    --primary-color-rgb: 159, 18, 57;
}

/* تنسيقات قسم الإحصائيات */
.statistics-section {
    margin-bottom: 25px;
}

.stats-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stats-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius, 8px);
    box-shadow: var(--card-shadow);
    padding: 20px;
    display: flex;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    position: relative;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.stats-icon {
    font-size: 2rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    width: 50px;
}

.stats-content {
    flex: 1;
}

.stats-content h3 {
    font-size: 1rem;
    color: var(--secondary-color);
    margin: 0 0 8px 0;
    font-weight: 600;
}

.stats-content p {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 5px 0;
    white-space: nowrap;
}

.stats-content span {
    font-size: 0.85rem;
    color: var(--text-muted);
}

/* تنسيقات خاصة بكل بطاقة */
.stats-card.deposits .stats-icon {
    color: var(--success-color);
}

.stats-card.withdrawals .stats-icon {
    color: var(--danger-color);
}

.stats-card.net-balance .stats-icon {
    color: var(--warning-color);
}

.positive-amount {
    color: var(--success-color) !important;
}

.negative-amount {
    color: var(--danger-color) !important;
}

/* Pay Due Button Styles */
.banks-sidebar-card .sidebar-btn.pay-due-btn {
    background: linear-gradient(135deg, #9f1239, #7f1d1d);
    color: var(--text-light);
    font-weight: 600;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.banks-sidebar-card .sidebar-btn.pay-due-btn:hover {
    background: linear-gradient(135deg, #7f1d1d, #881337);
    box-shadow: 0 4px 15px rgba(159, 18, 57, 0.3);
    transform: translateY(-1px);
}

.banks-sidebar-card .sidebar-btn.pay-due-btn i {
    font-size: 1.1rem;
}

/* Pay Due Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content.large {
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    background-color: var(--card-bg);
    border-radius: 12px;
    box-shadow: var(--hover-shadow);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, #9f1239, #7f1d1d);
    color: var(--text-light);
    border-radius: 12px 12px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 20px;
}

/* Step Container */
.step-container {
    position: relative;
}

.step {
    display: none;
}

.step.active {
    display: block;
}

.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
}

.back-btn {
    background: var(--secondary-color);
    color: var(--text-light);
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.back-btn:hover {
    background: #34495e;
}

.step h4 {
    margin: 0;
    color: var(--text-dark);
    font-size: 1.3rem;
}

/* Banks List */
.banks-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.bank-item {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.bank-item:hover {
    border-color: #9f1239;
    box-shadow: var(--card-shadow);
    transform: translateY(-2px);
}

.bank-item.selected {
    border-color: #9f1239;
    background: #fce7f3;
}

.bank-item h5 {
    margin: 0 0 8px 0;
    color: var(--text-dark);
    font-size: 1.1rem;
}

.bank-item p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Transactions List */
.transactions-list {
    margin-top: 15px;
}

.transaction-item {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.transaction-item:hover {
    box-shadow: var(--card-shadow);
}

.transaction-info {
    flex: 1;
}

.transaction-info h6 {
    margin: 0 0 5px 0;
    color: var(--text-dark);
    font-size: 1rem;
}

.transaction-info p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.transaction-amount {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--success-color);
    margin: 0 15px;
}

.pay-transaction-btn {
    background: #9f1239;
    color: var(--text-light);
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-weight: 500;
}

.pay-transaction-btn:hover {
    background: #7f1d1d;
}

/* Payment Summary */
.payment-summary {
    background: #fce7f3;
    border-radius: 8px;
    padding: 15px;
    margin: 20px 0;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 500;
}

.summary-item:last-child {
    margin-bottom: 0;
    font-size: 1.1rem;
    color: #9f1239;
}

/* Amount Note */
.amount-note {
    display: block;
    margin-top: 5px;
    color: var(--text-muted);
    font-size: 0.85rem;
}

.amount-note span {
    font-weight: 600;
    color: #9f1239;
}

/* Clickable Amount */
.clickable-amount {
    cursor: pointer;
    color: var(--primary-color);
    text-decoration: underline;
    transition: color 0.3s ease;
}

.clickable-amount:hover {
    color: var(--primary-dark);
}

/* Payment Status Indicators */
.payment-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.payment-status.fully-paid {
    background: #d1fae5;
    color: #065f46;
}

.payment-status.partially-paid {
    background: #fef3c7;
    color: #92400e;
}

.payment-status.unpaid {
    background: #fee2e2;
    color: #991b1b;
}

/* Payment Counter */
.payment-counter {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background: var(--info-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.payment-counter:hover {
    background: #2980b9;
}

/* Transaction Details Modal */
.payment-history-modal,
.transaction-details-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    z-index: 10001;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
}

.payment-history-modal .modal-header,
.transaction-details-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
    background: var(--primary-color);
    color: white;
    border-radius: 12px 12px 0 0;
}

.payment-history-modal .modal-header h4,
.transaction-details-modal .modal-header h4 {
    margin: 0;
    font-size: 1.2rem;
}

.payment-history-modal .close-btn,
.transaction-details-modal .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.payment-history-modal .close-btn:hover,
.transaction-details-modal .close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.payment-history-modal .modal-body,
.transaction-details-modal .modal-body {
    padding: 20px;
}

/* Payment Items */
.payment-item,
.payment-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 10px;
    background: #f9fafb;
    transition: all 0.3s ease;
}

.payment-item:hover,
.payment-card:hover {
    background: #f3f4f6;
    border-color: var(--primary-color);
}

.payment-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.payment-number,
.payment-date {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.payment-amount {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.payment-bank {
    font-size: 0.85rem;
    color: var(--text-muted);
    font-style: italic;
}

/* Transaction Card */
.transaction-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    background: #f8fafc;
    margin-bottom: 20px;
}

.transaction-card .transaction-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.transaction-card .transaction-info span {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.transaction-card .transaction-amount {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--success-color);
}

/* Summary Section */
.summary-section {
    border-top: 1px solid #e5e7eb;
    padding-top: 20px;
    margin-top: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f3f4f6;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item.highlight {
    background: #fef3c7;
    padding: 15px;
    border-radius: 8px;
    font-weight: 600;
    color: #92400e;
}

/* Section Headers */
.original-transaction h5,
.payments-section h5 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 5px;
}

/* Fully Paid Amount */
.fully-paid-amount {
    text-decoration: line-through;
    color: var(--text-muted);
    opacity: 0.7;
}

/* Payment Breakdown in Deposit List */
.payment-breakdown {
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin-top: 8px;
}

.payment-breakdown span {
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 4px;
}

.original-amount {
    background: #e5e7eb;
    color: #374151;
}

.paid-amount {
    background: #dbeafe;
    color: #1e40af;
}

.remaining-amount {
    background: #fef3c7;
    color: #92400e;
}

/* Transaction Actions */
.transaction-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
}

.payment-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Transaction Item States */
.transaction-item.fully-paid {
    background: #f0fdf4;
    border-color: #22c55e;
}

.transaction-item.partially-paid {
    background: #fffbeb;
    border-color: #f59e0b;
}

.transaction-item.unpaid {
    background: #fef2f2;
    border-color: #ef4444;
}

/* Table Row States */
.fully-paid-row {
    background: #f0fdf4 !important;
    color: #6b7280;
}

.fully-paid-row td {
    position: relative;
}

.fully-paid-row .amount-positive.fully-paid {
    text-decoration: line-through;
    color: #6b7280;
    opacity: 0.8;
}

.partial-paid-row {
    background: #fffbeb !important;
}

.excess-paid-row {
    background: #fef2f2 !important;
}

/* Payment Indicator */
.payment-indicator {
    display: inline-block;
    font-size: 0.75rem;
    color: #6b7280;
    margin-left: 8px;
    font-style: italic;
}

/* Details Button */
.details-btn {
    background: var(--info-color);
    color: white;
    border: none;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    margin-right: 5px;
    transition: all 0.3s ease;
}

.details-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

/* Calculation Details Modal */
.calculation-details-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    z-index: 10001;
    max-width: 700px;
    width: 95%;
    max-height: 85vh;
    overflow-y: auto;
}

.calculation-details-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
    background: linear-gradient(135deg, var(--primary-color), #2563eb);
    color: white;
    border-radius: 12px 12px 0 0;
}

.calculation-details-modal .modal-header h4 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.calculation-details-modal .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.calculation-details-modal .close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.calculation-details-modal .modal-body {
    padding: 25px;
}

/* Calculation Sections */
.calculation-section {
    margin-bottom: 25px;
}

.calculation-section h5 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Calculation Cards */
.calculation-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px;
    border-radius: 10px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    position: relative;
}

.calculation-card.original {
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    border: 2px solid #22c55e;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.1);
}

.calculation-card.payment {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border: 2px solid #f59e0b;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.1);
}

.calculation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.payment-number {
    position: absolute;
    top: -8px;
    left: 15px;
    background: #f59e0b;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.calculation-card .transaction-info {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.calculation-card .transaction-info span {
    font-size: 0.9rem;
    color: #374151;
}

.amount-display {
    font-size: 1.4rem;
    font-weight: 700;
    padding: 8px 16px;
    border-radius: 8px;
    min-width: 120px;
    text-align: center;
}

.amount-display.positive {
    background: #22c55e;
    color: white;
}

.amount-display.negative {
    background: #ef4444;
    color: white;
}

/* Calculation Summary */
.calculation-summary {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #cbd5e1;
    margin-top: 25px;
}

.calculation-summary h5 {
    color: #1e293b;
    margin-bottom: 20px;
    font-size: 1.2rem;
    text-align: center;
}

.calculation-steps {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.calculation-step {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.calculation-step span:first-child {
    font-weight: 500;
    color: #374151;
}

.calculation-step .positive {
    color: #22c55e;
    font-weight: 600;
}

.calculation-step .negative {
    color: #ef4444;
    font-weight: 600;
}

.calculation-divider {
    height: 2px;
    background: linear-gradient(to right, #cbd5e1, #94a3b8, #cbd5e1);
    margin: 8px 0;
    border-radius: 1px;
}

.calculation-result {
    padding: 16px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.calculation-result.fully-paid {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
}

.calculation-result.partially-paid {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.calculation-result.excess-paid {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

/* Formula Display */
.formula-display {
    margin-top: 20px;
    text-align: center;
}

.formula {
    background: #1e293b;
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 1px;
    box-shadow: 0 4px 12px rgba(30, 41, 59, 0.3);
}

.formula .positive {
    color: #22c55e;
}

.formula .negative {
    color: #ef4444;
}

/* No Payments Message */
.no-payments {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 20px;
    background: #f9fafb;
    border-radius: 8px;
    border: 2px dashed #d1d5db;
}

/* Loading States */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    color: var(--text-muted);
}

.loading-spinner i {
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 40px;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state h5 {
    margin: 0 0 10px 0;
    color: var(--text-dark);
}

.empty-state p {
    margin: 0;
    font-size: 0.9rem;
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .stats-cards-container {
        grid-template-columns: 1fr;
    }

    .stats-card {
        padding: 15px;
    }

    .stats-content p {
        font-size: 1.3rem;
    }

    .banks-list {
        grid-template-columns: 1fr;
    }

    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .transaction-amount {
        margin: 0;
    }
}
