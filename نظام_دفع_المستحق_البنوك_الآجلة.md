# نظام دفع المستحق من البنوك الآجلة

## نظرة عامة
تم إنشاء نظام متكامل لدفع المستحق من البنوك الآجلة يتضمن:
- واجهة مستخدم تفاعلية مع modal متعدد الخطوات
- triggers قاعدة بيانات تلقائية لمعالجة المعاملات العكسية
- نظام حذف تلقائي للمعاملات المتطابقة

## الملفات المحدثة

### 1. واجهة المستخدم
- **banks_section/banks.html**: إضافة أزرار العمل وmodal دفع المستحق
- **banks_section/style.css**: تصميم الأزرار والmodal والخطوات
- **banks_section/script.js**: منطق التطبيق والتفاعل مع قاعدة البيانات

### 2. قاعدة البيانات
- **deferred_bank_payment_triggers.sql**: ملف triggers قاعدة البيانات

## المكونات الرئيسية

### 1. الأزرار الجديدة
- **دفع المستحق**: يفتح modal لعملية الدفع
- **إضافة بنك جديد**: ينتقل لنموذج إضافة البنك
- **البنوك المركزية**: ينتقل لصفحة البنوك المركزية
- **مراجعة المعاملات**: ينتقل لصفحة معاملات البنوك الآجلة

### 2. Modal دفع المستحق (3 خطوات)

#### الخطوة 1: اختيار البنك الآجل
- عرض جميع البنوك الآجلة في شكل بطاقات
- إمكانية النقر لاختيار البنك

#### الخطوة 2: اختيار معاملة الإيداع
- عرض جميع معاملات الإيداع للبنك المختار
- زر دفع بجانب كل معاملة
- عرض تفاصيل المعاملة (التاريخ، المبلغ، الوصف)

#### الخطوة 3: تفاصيل الدفع
- اختيار البنك المركزي للدفع إليه
- تحديد تاريخ الدفع
- إضافة تفاصيل إضافية
- ملخص الدفع (المبلغ، البنك الآجل)

### 3. Triggers قاعدة البيانات

#### handle_deferred_bank_transactions()
- يراقب المعاملات الجديدة في جدول bank_transactions
- يتحقق من نوع البنك (آجل)
- يبحث عن المعاملات المتطابقة (إيداع/سحب بنفس المبلغ)
- يحذف المعاملات المتطابقة تلقائياً

#### financial_log_to_bank_transactions_trigger
- يزامن المعاملات من financial_transactions_log إلى bank_transactions
- موجود مسبقاً ويعمل بشكل طبيعي

## آلية العمل

### 1. عملية الدفع
1. المستخدم يختار "دفع المستحق"
2. يختار البنك الآجل من القائمة
3. يختار معاملة الإيداع المراد دفعها
4. يملأ تفاصيل الدفع (البنك المركزي، التاريخ، التفاصيل)
5. يضغط "تأكيد الدفع"

### 2. معالجة قاعدة البيانات
1. إنشاء سجل في financial_transactions_log
2. trigger يقوم بإنشاء معاملة في bank_transactions للبنك المركزي
3. إنشاء معاملة سحب عكسية في bank_transactions للبنك الآجل
4. trigger يتحقق من تطابق المبالغ ويحذف المعاملات المتطابقة

### 3. النتيجة النهائية
- تسجيل الدفع في financial_transactions_log
- إنشاء معاملة إيداع في البنك المركزي
- حذف معاملة الإيداع الأصلية من البنك الآجل (إذا تطابقت المبالغ)

## الاختبار

### البيانات التجريبية
- بنك آجل تجريبي: "بنك آجل تجريبي"
- بنك مركزي تجريبي: "بنك مركزي تجريبي"
- معاملات إيداع تجريبية بمبالغ مختلفة

### اختبار Triggers
تم اختبار triggers بنجاح:
- إنشاء معاملة إيداع (1500 ر.س)
- إنشاء معاملة سحب عكسية (1500 ر.س)
- حذف كلا المعاملتين تلقائياً لتطابق المبالغ

## الميزات

### 1. واجهة المستخدم
- تصميم احترافي مع ألوان مميزة للبنوك الآجلة
- modal متعدد الخطوات مع navigation سهل
- رسائل تحميل وحالات فارغة
- تصميم متجاوب

### 2. الأمان والموثوقية
- التحقق من صحة البيانات
- معالجة الأخطاء
- رسائل واضحة للمستخدم
- triggers تلقائية لضمان تطابق البيانات

### 3. الأداء
- تحميل البيانات بشكل تدريجي
- استعلامات محسنة
- معالجة تلقائية للمعاملات

## ملاحظات مهمة

### 1. أنواع البنوك
- النوع الصحيح للبنوك الآجلة في قاعدة البيانات: "اجل" (بدون همزة)
- النوع الصحيح للبنوك المركزية: "مركزي"

### 2. Triggers
- جميع triggers نشطة ومختبرة
- تعمل تلقائياً عند إدراج معاملات جديدة
- تحذف المعاملات المتطابقة فوراً

### 3. التكامل
- النظام متكامل مع باقي أجزاء التطبيق
- يستخدم نفس مكتبات التصميم والألوان
- متوافق مع نظام المصادقة الموجود

## الاستخدام

1. افتح صفحة البنوك الآجلة: `banks_section/banks.html`
2. اضغط على زر "دفع المستحق"
3. اتبع الخطوات الثلاث في Modal
4. تأكد من نجاح العملية من خلال الرسائل

## الدعم الفني

للتحقق من حالة triggers:
```sql
SELECT * FROM public.check_deferred_payment_triggers_status();
```

لعرض المعاملات الحديثة:
```sql
SELECT bt.*, b.name, b.bank_type 
FROM bank_transactions bt 
JOIN banks b ON bt.bank_id = b.id 
WHERE b.bank_type = 'اجل' 
ORDER BY bt.created_at DESC;
```
