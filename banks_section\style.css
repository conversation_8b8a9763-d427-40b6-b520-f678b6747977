/* Content copied from c:\Users\<USER>\OneDrive\سطح المكتب\مشروعي\buses_section\style.css */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

:root {
    /* Main Colors */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;

    /* Deferred Banks Colors */
    --deferred-primary: #9f1239;
    --deferred-dark: #7f1d1d;
    --deferred-light: #fce7f3;

    /* Backgrounds */
    --body-bg: #f5f7fa;
    --card-bg: #ffffff;

    /* Borders */
    --border-color: #e1e8ed;
    --border-radius: 8px;

    /* Typography */
    --text-dark: #34495e;
    --text-muted: #7f8c8d;
    --text-light: #ffffff;

    /* Shadow */
    --card-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 5px 25px rgba(0, 0, 0, 0.12);
    --button-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--body-bg);
    color: var(--text-dark);
    line-height: 1.6;
}

/* Dashboard Container */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-light);
    padding: 20px;
    text-align: center;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
}

.header-content h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.header-content p {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 300;
}

.header-content h1 i {
    margin-left: 10px;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
}

/* Stats Cards */
.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.stat-icon {
    font-size: 2rem;
    margin-left: 15px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-card:nth-child(1) .stat-icon {
    color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.1);
}

.stat-card:nth-child(2) .stat-icon {
    color: var(--success-color);
    background-color: rgba(46, 204, 113, 0.1);
}

.stat-card:nth-child(3) .stat-icon {
    color: var(--warning-color);
    background-color: rgba(243, 156, 18, 0.1);
}

.stat-info h3 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.stat-info p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Controls Section */
.controls-section {
    margin-bottom: 20px;
}

.control-card, .table-card, .form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa; /* Light header background */
}

.card-header h2 {
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0;
}

.card-body {
    padding: 20px 30px; /* Adjust padding */
    max-height: 75vh; /* Limit height and allow scrolling */
    overflow-y: auto;
}

.controls-grid {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 15px;
    align-items: center;
}

.control-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--text-light);
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
}

.control-btn i {
    font-size: 1rem;
}

.add-btn {
    background-color: var(--success-color);
}

.add-btn:hover {
    background-color: #27ae60;
}

.print-btn {
    background-color: var(--primary-color);
}

.print-btn:hover {
    background-color: var(--primary-dark);
}

.search-container {
    display: flex;
    max-width: 100%;
    position: relative;
}

#search-input {
    width: 100%;
    padding: 10px 45px 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

#search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

.search-btn {
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 1rem;
    padding: 5px;
    cursor: pointer;
}

/* Table */
.table-section {
    margin-bottom: 20px;
}

.badge {
    background-color: var(--primary-color);
    color: var(--text-light);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.table-responsive {
    overflow-x: auto;
}

#banks-table { /* Updated ID */
    width: 100%;
    border-collapse: collapse;
}

#banks-table th, /* Updated ID */
#banks-table td { /* Updated ID */
    padding: 12px 15px;
    text-align: right;
}

#banks-table th { /* Updated ID */
    background-color: rgba(52, 152, 219, 0.05);
    color: var(--secondary-color);
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
}

#banks-table tbody tr { /* Updated ID */
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

#banks-table tbody tr:last-child { /* Updated ID */
    border-bottom: none;
}

#banks-table tbody tr:hover { /* Updated ID */
    background-color: rgba(52, 152, 219, 0.05);
}

#banks-table .loading-message { /* Updated ID */
    text-align: center;
    color: var(--text-muted);
    padding: 20px;
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px 8px;
    margin: 0 3px;
    font-size: 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.action-btn.edit-btn {
    color: var(--warning-color);
}

.action-btn.edit-btn:hover {
    background-color: rgba(243, 156, 18, 0.1);
}

.action-btn.delete-btn {
    color: var(--danger-color);
}

.action-btn.delete-btn:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    gap: 5px;
}

.pagination button {
    background-color: var(--light-color);
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination button:hover:not(.disabled) {
    background-color: var(--primary-color);
    color: var(--text-light);
}

.pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Enhanced Form Section - Modal Styling */
.form-section {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px); /* Add blur effect to background */
    -webkit-backdrop-filter: blur(4px); /* For Safari */
    justify-content: center;
    align-items: center;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.form-section.show {
    display: flex;
    opacity: 1;
}

.form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 700px;
    margin: auto;
    position: relative;
    transform: translateY(30px);
    transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    overflow: hidden;
}

.form-section.show .form-card {
    transform: translateY(0);
}

.card-header {
    padding: 15px 25px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-btn:hover {
    color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
}

.card-body {
    padding: 20px 30px; /* Adjust padding */
    max-height: 75vh; /* Limit height and allow scrolling */
    overflow-y: auto;
}

/* Ensure this .form-row style is used inside the modal */
.form-row {
    display: grid;
    /* Adjust grid columns based on content, allow flexibility */
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 15px;
}

/* Style for fieldsets */
.form-fieldset {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px 20px 20px 20px; /* Add padding */
    margin-bottom: 25px; /* Space between fieldsets */
    position: relative;
}

.form-fieldset legend {
    font-weight: 600;
    color: var(--primary-dark);
    padding: 0 10px; /* Padding around legend text */
    margin-right: 10px; /* Position legend correctly in RTL */
    font-size: 1.1rem;
}

/* Style for required fields */
.required {
    color: var(--danger-color);
    margin-right: 3px;
}

/* Checkbox styling */
.form-group-checkbox {
    display: flex;
    align-items: center; /* Align checkbox and label vertically */
    padding-top: 25px; /* Align with input fields */
}

.form-group-checkbox label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 0; /* Remove default margin */
}

.form-group-checkbox input[type="checkbox"] {
    width: auto; /* Override default width */
    margin-left: 8px; /* Space between checkbox and text */
    accent-color: var(--primary-color); /* Style checkbox color */
    height: 18px;
    width: 18px;
}

/* File input styling */
.form-group input[type="file"] {
    padding: 8px; /* Adjust padding */
    border: 1px dashed var(--border-color); /* Dashed border */
    background-color: #f9f9f9;
}

.form-group input[type="file"]::file-selector-button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    background-color: var(--primary-color);
    color: var(--text-light);
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-left: 10px; /* Space in RTL */
}

.form-group input[type="file"]::file-selector-button:hover {
    background-color: var(--primary-dark);
}

/* Small text hint */
.form-group small {
    display: block;
    margin-top: 5px;
    font-size: 0.85rem;
    color: var(--text-muted);
}

/* Responsive adjustments for modal */
@media (max-width: 768px) {
    /* ... existing responsive styles ... */
    .form-row {
        grid-template-columns: 1fr; /* Stack form elements on smaller screens */
        gap: 15px; /* Adjusted gap for stacked */
    }
    .form-group-checkbox {
        padding-top: 0; /* Adjust alignment when stacked */
        margin-top: 10px;
    }
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea { /* Added textarea */
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: all 0.3s ease;
    font-family: inherit; /* Ensure textarea uses the same font */
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus { /* Added textarea */
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.submit-btn,
.cancel-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.submit-btn {
    background-color: var(--success-color);
    color: var(--text-light);
}

.submit-btn:hover {
    background-color: #27ae60;
}

.cancel-btn {
    background-color: var(--light-color);
    color: var(--text-dark);
}

.cancel-btn:hover {
    background-color: #bdc3c7;
}

/* Message Styles */
.message {
    margin-top: 15px;
    padding: 12px;
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 500;
    display: none;
}

.message.show {
    display: block;
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var (--success-color);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* Footer */
.dashboard-footer {
    background-color: var(--card-bg);
    text-align: center;
    padding: 15px;
    color: var(--text-muted);
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

/* Responsive */
@media (max-width: 992px) {
    .dashboard-main {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .stats-section {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .controls-grid {
        grid-template-columns: 1fr;
    }

    .search-container {
        order: -1;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

@media (max-width: 576px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .badge {
        margin-top: 5px;
        align-self: flex-start;
    }

    .form-actions {
        flex-direction: column;
    }

    .submit-btn,
    .cancel-btn {
        width: 100%;
    }

    .header-content h1 {
        font-size: 1.5rem;
    }
}

/* Mini form styles (for potential future use like adding buses) */
.mini-form-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.mini-form {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    width: 100%;
    max-width: 450px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    direction: rtl;
}

.mini-form h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--primary-dark);
    font-size: 1.2rem;
    text-align: center;
}

.mini-form-group {
    margin-bottom: 15px;
}

.mini-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.mini-form-group input,
.mini-form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.95rem;
}

.mini-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 25px;
}

.mini-form-actions button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.mini-form-save {
    background-color: var(--success-color);
    color: white;
}

.mini-form-cancel {
    background-color: var(--light-color);
    color: var(--text-dark);
}

.add-new-option {
    color: var(--primary-dark);
    font-weight: bold;
    background-color: #f0f8ff;
}

/* Status indicator styles */
.status-active { color: #2ecc71; font-weight: bold; }
.status-inactive { color: #e74c3c; font-weight: bold; }
/* Add specific status styles if needed */
.status-maintenance { color: var(--warning-color); font-weight: bold; }
.status-stopped { color: var(--danger-color); font-weight: bold; }


/* Additional pagination styles */
.pagination-ellipsis {
    padding: 8px 5px;
    color: var(--text-muted);
}
.pagination button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
.pagination button.active {
    background-color: var(--primary-color);
    color: var(--text-light);
    font-weight: bold;
}

/* Style for location button (if needed later) */
.location-btn {
    color: var(--primary-color);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: 1px solid var(--primary-color);
    border-radius: 4px;
}
.location-btn:hover {
    background-color: rgba(52, 152, 219, 0.1);
    text-decoration: none;
}
.location-btn i {
     margin-left: 4px; /* RTL */
}

/* Action Buttons Section */
.action-buttons-section {
    margin-bottom: 25px;
}

.action-buttons-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 20px;
}

.action-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: var(--text-light);
    font-weight: 600;
    min-height: 100px;
    position: relative;
    overflow: hidden;
}

.action-btn i {
    font-size: 2rem;
    margin-bottom: 8px;
}

.action-btn span {
    font-size: 1rem;
    text-align: center;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

/* Specific button colors */
.pay-due-btn {
    background: linear-gradient(135deg, var(--deferred-primary), var(--deferred-dark));
}

.pay-due-btn:hover {
    background: linear-gradient(135deg, var(--deferred-dark), #881337);
}

.add-bank-btn {
    background: linear-gradient(135deg, var(--success-color), #27ae60);
}

.add-bank-btn:hover {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.central-banks-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.central-banks-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), #1f4e79);
}

.review-btn {
    background: linear-gradient(135deg, var(--secondary-color), #34495e);
}

.review-btn:hover {
    background: linear-gradient(135deg, #34495e, #2c3e50);
}

/* Pay Due Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--hover-shadow);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--deferred-primary), var(--deferred-dark));
    color: var(--text-light);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 20px;
}

/* Step Container */
.step-container {
    position: relative;
}

.step {
    display: none;
}

.step.active {
    display: block;
}

.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
}

.back-btn {
    background: var(--secondary-color);
    color: var(--text-light);
    border: none;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.back-btn:hover {
    background: #34495e;
}

.step h4 {
    margin: 0;
    color: var(--text-dark);
    font-size: 1.3rem;
}

/* Banks List */
.banks-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.bank-item {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.bank-item:hover {
    border-color: var(--deferred-primary);
    box-shadow: var(--card-shadow);
    transform: translateY(-2px);
}

.bank-item.selected {
    border-color: var(--deferred-primary);
    background: var(--deferred-light);
}

.bank-item h5 {
    margin: 0 0 8px 0;
    color: var(--text-dark);
    font-size: 1.1rem;
}

.bank-item p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Transactions List */
.transactions-list {
    margin-top: 15px;
}

.transaction-item {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.transaction-item:hover {
    box-shadow: var(--card-shadow);
}

.transaction-info {
    flex: 1;
}

.transaction-info h6 {
    margin: 0 0 5px 0;
    color: var(--text-dark);
    font-size: 1rem;
}

.transaction-info p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.transaction-amount {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--success-color);
    margin: 0 15px;
}

.pay-transaction-btn {
    background: var(--deferred-primary);
    color: var(--text-light);
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-weight: 500;
}

.pay-transaction-btn:hover {
    background: var(--deferred-dark);
}

/* Payment Summary */
.payment-summary {
    background: var(--deferred-light);
    border-radius: var(--border-radius);
    padding: 15px;
    margin: 20px 0;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 500;
}

.summary-item:last-child {
    margin-bottom: 0;
    font-size: 1.1rem;
    color: var(--deferred-primary);
}

/* Loading States */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    color: var(--text-muted);
}

.loading-spinner i {
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 40px;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state h5 {
    margin: 0 0 10px 0;
    color: var(--text-dark);
}

.empty-state p {
    margin: 0;
    font-size: 0.9rem;
}
