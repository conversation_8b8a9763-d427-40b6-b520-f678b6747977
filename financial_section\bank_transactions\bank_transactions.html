<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المعاملات البنكية</title>

    <!-- الترتيب مهم جداً -->
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../../shared_styles.css">
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="style.css">
    <!-- Sidebar Component Styles -->
    <link rel="stylesheet" href="../../shared_components/sidebar.css">

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../../config.js"></script>
    <script src="../../auth.js"></script>
    <!-- Sidebar Component Script -->
    <script src="../../shared_components/sidebar.js"></script>
    <!-- Page Script -->
    <script defer src="script.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-layout" data-load-sidebar="true">
    <!-- Top Navigation Bar -->
    <nav class="top-navbar">
        <div class="navbar-left">
            <button id="sidebar-toggle" class="sidebar-toggle-btn">
                <i class="fas fa-bars"></i>
            </button>
            <div class="navbar-brand">
                <i class="fas fa-exchange-alt"></i>
                <span id="navbar-title">المعاملات البنكية</span>
            </div>
        </div>
        <div class="navbar-right">
            <!-- محتوى الجانب الأيمن من الشريط العلوي -->
            <div class="navbar-user">
                <span id="navbar-username"></span>
                <button id="logout-btn" class="logout-btn" style="display: none;">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Sidebar Container (will be populated by sidebar.js) -->
    <div id="sidebar-container"></div>

    <!-- Main Content Area -->
    <main class="main-content">
        <div class="content-wrapper">
            <!-- Banks Sidebar Section -->
            <div class="banks-sidebar-section">
                <div class="banks-sidebar-card">
                    <div class="sidebar-header">
                        <h3 id="sidebar-title">قائمة البنوك</h3>
                    </div>
                    <ul class="sidebar-list" id="banks-sidebar-list">
                        <li class="loading-message">جاري تحميل البنوك...</li>
                    </ul>
                    <div class="sidebar-footer">
                        <button id="pay-due-btn" class="sidebar-btn pay-due-btn" style="margin-bottom: 10px; display: none;">
                            <i class="fas fa-money-bill-wave"></i> دفع المستحق
                        </button>
                        <button id="add-bank-btn" class="sidebar-btn add-btn" style="margin-bottom: 10px;">
                            <i class="fas fa-plus-circle"></i> إضافة بنك جديد
                        </button>
                        <button id="switch-bank-type-btn" class="sidebar-btn switch-btn" style="margin-bottom: 10px;">
                            <i class="fas fa-exchange-alt"></i> <span id="switch-btn-text">البنوك الآجلة</span>
                        </button>
                        <button id="back-to-finance-btn-sidebar" class="sidebar-btn">
                            <i class="fas fa-arrow-left"></i> العودة للوحة المعلومات
                        </button>
                    </div>
                </div>
            </div>

            <!-- Main Dashboard Content -->
            <div class="main-dashboard-content">
                <!-- Page Header -->
                <header class="dashboard-page-header">
                    <h1 id="main-header-title"><i class="fas fa-university"></i> المعاملات البنكية</h1>
                    <p>عرض وإدارة المعاملات البنكية لنوع الحساب المحدد.</p>
                    <div id="dashboard-message" class="message" style="display: none;"></div>
                </header>

            <!-- Statistics Summary Cards -->
            <section class="statistics-section">
                <div class="stats-cards-container">
                    <div class="stats-card deposits">
                        <div class="stats-icon">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="stats-content">
                            <h3>إجمالي الإيداعات</h3>
                            <p id="total-deposits">0.00 ريال</p>
                            <span id="deposits-count">(0 معاملة)</span>
                        </div>
                    </div>
                    <div class="stats-card withdrawals">
                        <div class="stats-icon">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="stats-content">
                            <h3>إجمالي السحوبات</h3>
                            <p id="total-withdrawals">0.00 ريال</p>
                            <span id="withdrawals-count">(0 معاملة)</span>
                        </div>
                    </div>
                    <div class="stats-card net-balance">
                        <div class="stats-icon">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                        <div class="stats-content">
                            <h3>الصافي</h3>
                            <p id="net-balance">0.00 ريال</p>
                            <span id="balance-percentage">0%</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Filters Section -->
            <section class="controls-section filter-controls">
                 <div class="filter-card">
                     <div class="card-header">
                         <h2><i class="fas fa-filter"></i> تصفية المعاملات</h2>
                     </div>
                     <div class="card-body">
                         <div class="filter-grid">
                             <div class="form-group">
                                 <label for="filter-year">السنة النشطة:</label>
                                 <select id="filter-year">
                                     <option value="">كل السنوات</option>
                                     <!-- Options populated by JS -->
                                 </select>
                             </div>
                             <div class="form-group">
                                 <label for="filter-month">الشهر:</label>
                                 <select id="filter-month" disabled> <!-- Disabled initially -->
                                     <option value="">كل الشهور</option>
                                     <!-- Options populated by JS based on year selection -->
                                 </select>
                             </div>
                             <div class="form-group">
                                 <label for="filter-bank">البنك:</label>
                                 <!-- Options populated by JS based on type -->
                                 <select id="filter-bank">
                                     <option value="">كل البنوك المحددة</option>
                                 </select>
                             </div>
                             <div class="form-group">
                                 <label for="filter-type">نوع المعاملة:</label>
                                 <select id="filter-type">
                                     <option value="">الكل</option>
                                     <option value="deposit">إيداع</option>
                                     <option value="withdrawal">سحب</option>
                                     <option value="transfer_in">تحويل وارد</option>
                                     <option value="transfer_out">تحويل صادر</option>
                                     <option value="initial_balance">رصيد افتتاحي</option>
                                 </select>
                             </div>
                             <div class="form-group">
                                 <label for="filter-start-date">من تاريخ:</label>
                                 <input type="date" id="filter-start-date">
                             </div>
                             <div class="form-group">
                                 <label for="filter-end-date">إلى تاريخ:</label>
                                 <input type="date" id="filter-end-date">
                             </div>
                             <div class="form-group search-group">
                                 <label for="search-input">بحث بالوصف:</label>
                                 <input type="text" id="search-input" placeholder="أدخل كلمة للبحث...">
                             </div>
                             <div class="filter-actions">
                                 <button id="filter-btn" class="filter-btn apply-filters-btn"><i class="fas fa-check"></i> تطبيق</button>
                                 <button id="reset-filter-btn" class="filter-btn reset-filters-btn"><i class="fas fa-undo"></i> إعادة تعيين</button>
                             </div>
                         </div>
                     </div>
                 </div>
            </section>

            <!-- Transactions Table Section -->
            <section class="table-section">
                <div class="table-card">
                    <div class="card-header">
                        <h2><i class="fas fa-list"></i> قائمة المعاملات</h2>
                        <div>
                            <button id="add-transaction-btn" class="control-btn add-btn">
                                <i class="fas fa-plus"></i> إضافة معاملة يدوية
                            </button>
                            <span class="badge" id="transactions-count">0</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="transactions-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>البنك</th>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>المبلغ</th>
                                        <th>الوصف</th>
                                        <th>المرجع</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="transactions-tbody">
                                    <!-- Initial message -->
                                    <tr><td colspan="8" class="loading-message">الرجاء اختيار بنك من القائمة الجانبية أو تطبيق الفلاتر لعرض المعاملات.</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div id="list-message" class="message" style="display: none;"></div>
                        <div class="pagination" id="pagination-controls">
                            <!-- Pagination controls -->
                        </div>
                    </div>
                </div>
            </section>

            </div> <!-- Close main-dashboard-content -->
        </div> <!-- Close content-wrapper -->
    </main> <!-- Close main-content -->

    <!-- Add/Edit Transaction Modal -->
    <div id="transaction-modal" class="modal">
        <div class="modal-content large"> <!-- Added 'large' class if needed -->
            <span id="close-transaction-modal-btn" class="close-modal-btn">&times;</span>
            <h2 id="transaction-form-title">إضافة معاملة بنكية يدوية</h2>
            <form id="transaction-form">
                <input type="hidden" id="transaction_id" name="transaction_id">
                <input type="hidden" id="reference_table" name="reference_table">
                <input type="hidden" id="reference_id" name="reference_id">
                <div class="form-grid">
                    <!-- New Year and Month Fields -->
                    <div class="form-group">
                        <label for="transaction-year">السنة المالية</label> <!-- Optional, but recommended -->
                        <select id="transaction-year" name="budget_year_id">
                            <option value="">-- اختر السنة --</option>
                            <!-- Options populated by JS -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="transaction-month">الشهر المالي</label> <!-- Optional, but recommended -->
                        <select id="transaction-month" name="budget_month_id" disabled>
                            <option value="">-- اختر الشهر --</option>
                            <!-- Options populated by JS -->
                        </select>
                    </div>
                    <!-- Existing Fields -->
                    <div class="form-group">
                        <label for="bank_id">البنك <span class="required">*</span></label>
                        <!-- Options populated by JS based on type -->
                        <select id="bank_id" name="bank_id" required>
                            <option value="" disabled selected>اختر البنك...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="transaction_date">تاريخ المعاملة <span class="required">*</span></label>
                        <input type="date" id="transaction_date" name="transaction_date" required>
                    </div>
                    <div class="form-group">
                        <label for="transaction_type">نوع المعاملة <span class="required">*</span></label>
                        <select id="transaction_type" name="transaction_type" required>
                            <option value="" disabled selected>اختر النوع...</option>
                            <option value="deposit">إيداع</option>
                            <option value="withdrawal">سحب</option>
                            <option value="transfer_in">تحويل وارد</option>
                            <option value="transfer_out">تحويل صادر</option>
                            <!-- <option value="initial_balance">رصيد افتتاحي</option> --> <!-- Usually not added manually -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="amount">المبلغ <span class="required">*</span></label>
                        <input type="number" id="amount" name="amount" step="0.01" min="0.01" required placeholder="0.00">
                    </div>
                    <div class="form-group form-group-full-width">
                        <label for="description">الوصف</label>
                        <textarea id="description" name="description" rows="3" placeholder="وصف المعاملة (اختياري)"></textarea>
                    </div>
                </div>
                <div id="transaction-form-message" class="message" style="display: none;"></div>
                <div class="form-actions">
                    <button type="submit" class="submit-btn"><i class="fas fa-save"></i> حفظ المعاملة</button>
                    <button type="button" class="cancel-btn" onclick="closeTransactionModal()">إلغاء</button> <!-- Simple inline close -->
                </div>
            </form>
        </div>
    </div>

    <!-- Add Bank Modal -->
    <div id="add-bank-modal" class="modal">
        <div class="modal-content">
            <span id="close-add-bank-modal-btn" class="close-modal-btn">&times;</span>
            <h2>إضافة بنك جديد</h2>
            <form id="add-bank-form">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="bank_name">اسم البنك <span class="required">*</span></label>
                        <input type="text" id="bank_name" name="bank_name" required placeholder="اسم البنك">
                    </div>
                    <div class="form-group">
                        <label for="account_number">رقم الحساب</label>
                        <input type="text" id="account_number" name="account_number" placeholder="رقم الحساب البنكي">
                    </div>
                    <div class="form-group">
                        <label for="branch">الفرع</label>
                        <input type="text" id="branch" name="branch" placeholder="فرع البنك">
                    </div>
                    <div class="form-group">
                        <label for="contact_phone">رقم الاتصال</label>
                        <input type="tel" id="contact_phone" name="contact_phone" placeholder="رقم الهاتف">
                    </div>
                    <div class="form-group form-group-full-width">
                        <label for="notes">ملاحظات</label>
                        <textarea id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية"></textarea>
                    </div>
                    <!-- Hidden field for bank_type - will be set by JS -->
                    <input type="hidden" id="bank_type" name="bank_type" value="">
                </div>
                <div id="add-bank-form-message" class="message" style="display: none;"></div>
                <div class="form-actions">
                    <button type="submit" class="submit-btn"><i class="fas fa-save"></i> حفظ البنك</button>
                    <button type="button" class="cancel-btn" onclick="closeAddBankModal()"><i class="fas fa-times"></i> إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Pay Due Modal -->
    <div id="pay-due-modal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3><i class="fas fa-money-bill-wave"></i> دفع المستحق</h3>
                <button class="close-btn" id="close-pay-due-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="step-container">
                    <!-- Step 1: Select Deferred Bank -->
                    <div id="step-1" class="step active">
                        <h4>اختر البنك الآجل:</h4>
                        <div id="deferred-banks-list" class="banks-list">
                            <!-- Banks will be loaded here -->
                        </div>
                    </div>

                    <!-- Step 2: Select Deposit Transaction -->
                    <div id="step-2" class="step">
                        <div class="step-header">
                            <button id="back-to-step-1" class="back-btn">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                            <h4 id="selected-bank-name">معاملات الإيداع</h4>
                        </div>
                        <div id="deposit-transactions-list" class="transactions-list">
                            <!-- Transactions will be loaded here -->
                        </div>
                    </div>

                    <!-- Step 3: Payment Form -->
                    <div id="step-3" class="step">
                        <div class="step-header">
                            <button id="back-to-step-2" class="back-btn">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                            <h4>تفاصيل الدفع</h4>
                        </div>
                        <form id="payment-form">
                            <div class="form-group">
                                <label for="central-bank-select">البنك المركزي:</label>
                                <select id="central-bank-select" required>
                                    <option value="">اختر البنك المركزي</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="payment-amount">المبلغ المراد دفعه:</label>
                                <input type="number" id="payment-amount" step="0.01" min="0.01" required placeholder="0.00">
                                <small class="amount-note">الحد الأقصى: <span id="max-amount">0</span> ريال</small>
                            </div>
                            <div class="form-group">
                                <label for="payment-date">تاريخ الدفع:</label>
                                <input type="date" id="payment-date" required>
                            </div>
                            <div class="form-group">
                                <label for="payment-details">التفاصيل:</label>
                                <textarea id="payment-details" rows="3" placeholder="تفاصيل إضافية عن الدفع"></textarea>
                            </div>
                            <div class="payment-summary">
                                <div class="summary-item">
                                    <span>المبلغ الأصلي:</span>
                                    <span id="original-amount">0</span>
                                </div>
                                <div class="summary-item">
                                    <span>المبلغ المدفوع سابقاً:</span>
                                    <span id="paid-amount">0</span>
                                </div>
                                <div class="summary-item">
                                    <span>المبلغ المتبقي:</span>
                                    <span id="due-amount">0</span>
                                </div>
                                <div class="summary-item">
                                    <span>البنك الآجل:</span>
                                    <span id="deferred-bank-name">-</span>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="submit-btn">
                                    <i class="fas fa-check"></i> تأكيد الدفع
                                </button>
                                <button type="button" id="cancel-payment" class="cancel-btn">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div id="pay-due-message" class="message"></div>
            </div>
        </div>
    </div>

    <!-- Footer (Optional) -->
    <footer class="main-footer">
        <p>&copy; 2024 نظام الإدارة المالية</p>
    </footer>

</body>
</html>
