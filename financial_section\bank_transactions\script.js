console.log("Bank Transactions script loaded.");

// --- Auth Check ---
checkAuth('../../auth.js'); // Adjust path as needed

// --- Supabase Initialization ---
let supabaseUrl, supabaseAnonKey;
if (typeof SUPABASE_URL !== 'undefined') supabaseUrl = SUPABASE_URL;
else console.error('SUPABASE_URL not found.');
if (typeof SUPABASE_ANON_KEY !== 'undefined') supabaseAnonKey = SUPABASE_ANON_KEY;
else console.error('SUPABASE_ANON_KEY not found.');

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized for Bank Transactions:', _supabase);

// --- DOM Elements ---
// Common
const dashboardMessage = document.getElementById('dashboard-message');
const backToFinanceBtnSidebar = document.getElementById('back-to-finance-btn-sidebar');
const navbarTitle = document.getElementById('navbar-title');
const sidebarTitle = document.getElementById('sidebar-title');
const mainHeaderTitle = document.getElementById('main-header-title');
const banksSidebarList = document.getElementById('banks-sidebar-list'); // Added

// Filters
const filterYearSelect = document.getElementById('filter-year'); // New
const filterMonthSelect = document.getElementById('filter-month'); // New
const filterBankSelect = document.getElementById('filter-bank');
const filterTypeSelect = document.getElementById('filter-type');
const filterStartDateInput = document.getElementById('filter-start-date');
const filterEndDateInput = document.getElementById('filter-end-date');
const searchInput = document.getElementById('search-input');
const filterBtn = document.getElementById('filter-btn');
const resetFilterBtn = document.getElementById('reset-filter-btn');

// Table
const transactionsTableBody = document.getElementById('transactions-tbody');
const transactionsCountBadge = document.getElementById('transactions-count');
const listMessage = document.getElementById('list-message');
const paginationControls = document.getElementById('pagination-controls');

// Add/Edit Modal
const transactionModal = document.getElementById('transaction-modal');
const transactionForm = document.getElementById('transaction-form');
const transactionFormTitle = document.getElementById('transaction-form-title');
const closeTransactionModalBtn = document.getElementById('close-transaction-modal-btn');
const transactionFormMessage = document.getElementById('transaction-form-message');
const transactionIdField = document.getElementById('transaction_id');
const transactionYearSelect = document.getElementById('transaction-year'); // New
const transactionMonthSelect = document.getElementById('transaction-month'); // New
const bankIdSelect = document.getElementById('bank_id');
const transactionDateInput = document.getElementById('transaction_date');
const transactionTypeSelect = document.getElementById('transaction_type');
const amountInput = document.getElementById('amount');
const descriptionInput = document.getElementById('description');
const referenceTableInput = document.getElementById('reference_table'); // Hidden
const referenceIdInput = document.getElementById('reference_id');       // Hidden

// --- Additional DOM Elements ---
const addBankBtn = document.getElementById('add-bank-btn');
const addBankModal = document.getElementById('add-bank-modal');
const closeAddBankModalBtn = document.getElementById('close-add-bank-modal-btn');
const addBankForm = document.getElementById('add-bank-form');
const bankTypeField = document.getElementById('bank_type');
const addBankFormMessage = document.getElementById('add-bank-form-message');

// Statistics Cards Elements
const totalDepositsElement = document.getElementById('total-deposits');
const totalWithdrawalsElement = document.getElementById('total-withdrawals');
const netBalanceElement = document.getElementById('net-balance');
const depositsCountElement = document.getElementById('deposits-count');
const withdrawalsCountElement = document.getElementById('withdrawals-count');
const balancePercentageElement = document.getElementById('balance-percentage');

// Pay Due Modal Elements
const payDueBtn = document.getElementById('pay-due-btn');
const payDueModal = document.getElementById('pay-due-modal');
const closePayDueModal = document.getElementById('close-pay-due-modal');
const deferredBanksList = document.getElementById('deferred-banks-list');
const depositTransactionsList = document.getElementById('deposit-transactions-list');
const centralBankSelect = document.getElementById('central-bank-select');
const paymentForm = document.getElementById('payment-form');
const payDueMessage = document.getElementById('pay-due-message');

// Step navigation elements
const step1 = document.getElementById('step-1');
const step2 = document.getElementById('step-2');
const step3 = document.getElementById('step-3');
const backToStep1 = document.getElementById('back-to-step-1');
const backToStep2 = document.getElementById('back-to-step-2');
const selectedBankName = document.getElementById('selected-bank-name');
const dueAmount = document.getElementById('due-amount');
const deferredBankName = document.getElementById('deferred-bank-name');
const paymentDate = document.getElementById('payment-date');
const cancelPayment = document.getElementById('cancel-payment');

// --- State ---
let availableBanks = []; // Will hold banks filtered by type
let availableYears = []; // New: Store active years
let availableMonths = []; // New: Store months for selected year
let modalAvailableMonths = []; // New: Store months for the modal dropdown
let currentTransactions = [];
let currentPage = 1;
const itemsPerPage = 20;
let totalItems = 0;
let selectedBankType = null; // To store 'مركزي' or 'آجل'
let currentFilters = {
    yearId: '',   // New
    monthId: '',  // New
    bankId: '', // Can be set by sidebar click or filter dropdown
    type: '',
    startDate: '',
    endDate: '',
    searchTerm: ''
};

// اضافة متغيرات حالة للإحصائيات
let bankStatistics = {
    totalDeposits: 0,
    totalWithdrawals: 0,
    depositsCount: 0,
    withdrawalsCount: 0,
    netBalance: 0,
    balancePercentage: 0
};

// Pay Due Modal State
let selectedDeferredBank = null;
let selectedTransaction = null;
let centralBanks = [];
let deferredBanks = [];
let currentStep = 1;

// --- Helper Functions ---
const showMessage = (element, message, type = 'info', duration = 3000) => {
    if (!element) return;
    element.className = `message ${type}`;
    element.textContent = message;
    element.style.display = 'block';
    if (duration > 0) {
        setTimeout(() => {
            if (element.textContent === message) {
                element.style.display = 'none';
            }
        }, duration);
    }
};

const formatCurrency = (amount) => {
    const value = parseFloat(amount);
    return isNaN(value) ? '0.00' : value.toFixed(2);
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    } catch (e) {
        console.error("Error formatting date:", e);
        return '';
    }
};

const translateTransactionType = (type) => {
    switch (type) {
        case 'deposit':
        case 'إيداع': return 'إيداع';
        case 'withdrawal':
        case 'سحب': return 'سحب';
        case 'transfer_in': return 'تحويل وارد';
        case 'transfer_out': return 'تحويل صادر';
        case 'initial_balance': return 'رصيد افتتاحي';
        case 'مصروفات': return 'مصروفات';
        case 'استرداد': return 'استرداد';
        default: return type || 'غير محدد';
    }
};

// Function to get month name from number (Arabic) - Added
const getMonthName = (monthNumber) => {
    const names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"];
    const index = parseInt(monthNumber, 10) - 1;
    return names[index] || `شهر ${monthNumber}`;
};

// دالة للحصول على السنة والشهر الحالي من sessionStorage
const getCurrentYearAndMonth = () => {
    const selectedMonthId = sessionStorage.getItem('selectedBudgetMonthId');
    const selectedMonthNumber = sessionStorage.getItem('selectedBudgetMonthNumber');
    const selectedYearNumber = sessionStorage.getItem('selectedBudgetYearNumber');

    return {
        monthId: selectedMonthId,
        monthNumber: selectedMonthNumber,
        yearNumber: selectedYearNumber
    };
};

// دالة للحصول على معرف السنة من رقم السنة
const getYearIdFromYearNumber = (yearNumber) => {
    if (!yearNumber || !availableYears) return null;
    const year = availableYears.find(y => y.year_number == yearNumber);
    return year ? year.id : null;
};

// دالة للحصول على معرف الشهر من رقم الشهر
const getMonthIdFromMonthNumber = (monthNumber, yearId) => {
    if (!monthNumber || !yearId || !availableMonths) return null;
    const month = availableMonths.find(m => m.month_number == monthNumber);
    return month ? month.id : null;
};

// --- URL Parameter Handling ---
const getBankTypeFromURL = () => {
    const params = new URLSearchParams(window.location.search);
    const type = params.get('type');

    // تعديل لمعالجة النوعين بنفس الطريقة في قاعدة البيانات (التحقق من النوع في الرابط)
    if (type === 'مركزي') {
        return 'مركزي'; // بقي كما هو
    } else if (type === 'آجل' || type === 'اجل') {
        return 'اجل'; // إرجاع القيمة كما هي في قاعدة البيانات
    }

    // Fallback or error handling if type is missing or invalid
    console.warn('Bank type not specified or invalid in URL. Defaulting or showing error.');
    showMessage(dashboardMessage, 'خطأ: نوع البنك غير محدد في الرابط. الرجاء العودة واختيار النوع.', 'error', 0);
    // Optionally redirect back or disable functionality
    return null;
};

// --- Data Fetching ---

// New: Fetch Active Years (Modified to populate both filter and modal)
const fetchActiveYears = async () => {
    console.log("Fetching active years...");
    // Disable both selects initially
    if (filterYearSelect) {
        filterYearSelect.innerHTML = '<option value="">جاري التحميل...</option>';
        filterYearSelect.disabled = true;
    }
    if (transactionYearSelect) {
        transactionYearSelect.innerHTML = '<option value="">جاري التحميل...</option>';
        transactionYearSelect.disabled = true;
    }

    try {
        const { data, error } = await _supabase
            .from('budget_years')
            .select('id, year_number')
            .eq('is_active', true)
            .order('year_number', { ascending: false });

        if (error) throw error;

        availableYears = data || [];
        console.log("Fetched active years:", availableYears);
        populateYearFilter(); // Populate both dropdowns

    } catch (error) {
        console.error('Error fetching active years:', error);
        showMessage(dashboardMessage, `خطأ في جلب السنوات النشطة: ${error.message}`, 'error');
        if (filterYearSelect) filterYearSelect.innerHTML = '<option value="">خطأ</option>';
        if (transactionYearSelect) transactionYearSelect.innerHTML = '<option value="">خطأ</option>';
    } finally {
        // Enable both selects
        if (filterYearSelect) filterYearSelect.disabled = false;
        if (transactionYearSelect) transactionYearSelect.disabled = false;
    }
};

// New: Fetch Months for a Selected Year (Filter Dropdown)
const fetchBudgetMonthsForYear = async (yearId) => {
    // ... existing code to fetch and populate filterMonthSelect ...
    console.log(`Fetching months for year ID (Filter): ${yearId}`);
    if (!filterMonthSelect) return;
    filterMonthSelect.innerHTML = '<option value="">جاري التحميل...</option>';
    filterMonthSelect.disabled = true;
    availableMonths = []; // Clear previous months

    if (!yearId) {
        filterMonthSelect.innerHTML = '<option value="">كل الشهور</option>';
        // Keep it disabled as no year is selected
        return;
    }

    try {
        const { data, error } = await _supabase
            .from('budget_months')
            .select('id, month_number')
            .eq('budget_year_id', yearId)
            .order('month_number', { ascending: true });

        if (error) throw error;

        availableMonths = data || [];
        console.log(`Fetched ${availableMonths.length} months for year ${yearId} (Filter):`, availableMonths);
        populateMonthFilter(); // Populate the filter dropdown

    } catch (error) {
        console.error(`Error fetching months for year ${yearId} (Filter):`, error);
        showMessage(dashboardMessage, `خطأ في جلب شهور السنة: ${error.message}`, 'error');
        filterMonthSelect.innerHTML = '<option value="">خطأ</option>';
    } finally {
        // Enable month dropdown only if a year is selected and months were fetched (or attempted)
        filterMonthSelect.disabled = !yearId;
    }
};

// New: Fetch Months for a Selected Year (Modal Dropdown)
const fetchModalMonthsForYear = async (yearId) => {
    console.log(`Fetching months for year ID (Modal): ${yearId}`);
    if (!transactionMonthSelect) return;
    transactionMonthSelect.innerHTML = '<option value="">جاري التحميل...</option>';
    transactionMonthSelect.disabled = true;
    modalAvailableMonths = []; // Use separate state for modal

    if (!yearId) {
        transactionMonthSelect.innerHTML = '<option value="">-- اختر الشهر --</option>';
        // Keep it disabled as no year is selected
        return;
    }

    try {
        const { data, error } = await _supabase
            .from('budget_months')
            .select('id, month_number')
            .eq('budget_year_id', yearId)
            .order('month_number', { ascending: true });

        if (error) throw error;

        modalAvailableMonths = data || [];
        console.log(`Fetched ${modalAvailableMonths.length} months for year ${yearId} (Modal):`, modalAvailableMonths);
        populateModalMonthFilter(); // Populate the modal dropdown

    } catch (error) {
        console.error(`Error fetching months for year ${yearId} (Modal):`, error);
        // Show error in modal message area if possible
        if(transactionFormMessage) showMessage(transactionFormMessage, `خطأ في جلب شهور السنة: ${error.message}`, 'error', 0);
        transactionMonthSelect.innerHTML = '<option value="">خطأ</option>';
    } finally {
        // Enable month dropdown only if a year is selected
        transactionMonthSelect.disabled = !yearId;
    }
};


const fetchBanks = async (bankType) => {
    console.log(`Fetching banks with type: ${bankType}`); // Log the type being fetched
    if (!bankType) {
        availableBanks = [];
        populateBankFilter();
        populateBankDropdown();
        populateBanksSidebar(); // Ensure sidebar is updated even if type is missing
        console.warn("fetchBanks called without a valid bankType.");
        return;
    }
    if (banksSidebarList) banksSidebarList.innerHTML = `<li class="loading-message">جاري تحميل البنوك (${bankType})...</li>`; // Show loading in sidebar

    try {
        console.log(`Applying filter: bank_type = ${bankType}`); // Log the filter value
        const { data, error } = await _supabase
            .from('banks')
            .select('id, name, bank_type')
            .eq('bank_type', bankType)
            .order('name', { ascending: true });

        if (error) {
            // Throw the error to be caught by the catch block
            throw error;
        }

        availableBanks = data || [];
        console.log(`Fetched ${availableBanks.length} banks:`, availableBanks); // Log the fetched data

        // Populate UI elements AFTER data is fetched
        populateBankFilter();
        populateBankDropdown();
        populateBanksSidebar(); // Populate the sidebar list

    } catch (error) {
        console.error('Error fetching banks:', error);
        showMessage(dashboardMessage, `خطأ في جلب قائمة البنوك (${bankType}): ${error.message}`, 'error', 0); // Show error for longer
        availableBanks = []; // Ensure banks list is empty on error
        // Update UI elements to reflect the error state
        populateBankFilter();
        populateBankDropdown();
        populateBanksSidebar(); // Show empty/error state in sidebar
    }
};

const fetchTransactions = async () => {
    transactionsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message">جاري تحميل المعاملات...</td></tr>`;
    if (paginationControls) paginationControls.innerHTML = '';
    if (listMessage) listMessage.style.display = 'none';
    if (transactionsCountBadge) transactionsCountBadge.textContent = '0';

    // Ensure we have banks of the selected type before querying transactions
    if (availableBanks.length === 0 && !currentFilters.bankId) {
         transactionsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message">لا توجد بنوك من النوع المحدد (${selectedBankType || 'غير معروف'}).</td></tr>`;
         return;
    }

    try {
        let query = _supabase
            .from('bank_transactions')
            .select('*, banks!inner(name, bank_type)', { count: 'exact' }); // Use inner join to filter by bank_type

        // Filter by Bank Type (implicitly done by joining with filtered banks)
        query = query.eq('banks.bank_type', selectedBankType);

        // *** New: Filter by Month ***
        // IMPORTANT: Assumes 'budget_month_id' column exists on 'bank_transactions' table
        if (currentFilters.monthId) {
            query = query.eq('budget_month_id', currentFilters.monthId);
        } else if (currentFilters.yearId) {
            // If only year is selected, filter transactions within that year's date range
            // This requires fetching year start/end dates or joining through budget_months
            // Simpler approach for now: rely on month selection or date range filters
            console.warn("Year selected but month not selected. Filtering by date range is recommended for year-only filtering.");
            // Alternatively, if budget_month_id is nullable, you might filter where it's null
            // OR fetch all month IDs for the year and use .in('budget_month_id', monthIds)
        }

        // Apply Specific Bank Filter (from sidebar or dropdown) - only if not "all"
        if (currentFilters.bankId && currentFilters.bankId !== '') {
            query = query.eq('bank_id', currentFilters.bankId);
        }
        // Apply other filters
        if (currentFilters.type) {
            query = query.eq('transaction_type', currentFilters.type);
        }
        // Only apply date filters if month filter is NOT active
        if (!currentFilters.monthId && currentFilters.startDate) {
            query = query.gte('transaction_date', currentFilters.startDate);
        }
        if (!currentFilters.monthId && currentFilters.endDate) {
            query = query.lte('transaction_date', currentFilters.endDate);
        }
        if (currentFilters.searchTerm) {
            query = query.ilike('description', `%${currentFilters.searchTerm}%`);
        }

        // Ordering
        query = query.order('transaction_date', { ascending: false }).order('created_at', { ascending: false });

        // Pagination
        const startIndex = (currentPage - 1) * itemsPerPage;
        query = query.range(startIndex, startIndex + itemsPerPage - 1);

        const { data, error, count } = await query;

        if (error) throw error;

        currentTransactions = data || [];
        totalItems = count || 0;

        if (transactionsCountBadge) transactionsCountBadge.textContent = totalItems;
        await renderTransactionsTable();
        renderPaginationControls();

        if (totalItems === 0) {
            let noDataMessage = 'لا توجد معاملات تطابق معايير البحث.';
            if ((!currentFilters.bankId || currentFilters.bankId === '') && availableBanks.length === 0) {
                 noDataMessage = `لا توجد بنوك أو معاملات للنوع المحدد (${selectedBankType}).`;
            } else if ((!currentFilters.bankId || currentFilters.bankId === '') && availableBanks.length > 0) {
                noDataMessage = `لا توجد معاملات للبنوك ${selectedBankType === 'مركزي' ? 'المركزية' : 'الآجلة'} حسب الفلاتر المحددة.`;
            }
            showMessage(listMessage, noDataMessage, 'info');
            transactionsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message">${noDataMessage}</td></tr>`;
        }

    } catch (error) {
        console.error('Error fetching transactions:', error);
        showMessage(listMessage, `خطأ في جلب المعاملات: ${error.message}`, 'error', 0);
        transactionsTableBody.innerHTML = `<tr><td colspan="8" class="loading-message error">خطأ في تحميل البيانات.</td></tr>`;
    }
};

// --- دالة جديدة لجلب الإحصائيات البنكية ---
const fetchBankStatistics = async () => {
    if (!_supabase || !selectedBankType) return;

    try {
        let query = _supabase
            .from('bank_transactions')
            .select('transaction_type, amount, banks!inner(bank_type)');

        // فلترة حسب نوع البنك
        query = query.eq('banks.bank_type', selectedBankType);

        // *** New: Filter by Month ***
        if (currentFilters.monthId) {
            query = query.eq('budget_month_id', currentFilters.monthId);
        }
        // *** End New ***

        // تطبيق فلترة البنك المحدد إن وجد (فقط إذا لم يكن "الكل")
        if (currentFilters.bankId && currentFilters.bankId !== '') {
            query = query.eq('bank_id', currentFilters.bankId);
        }

        // تطبيق فلترة التاريخ إن وجدت (only if month is not selected)
        if (!currentFilters.monthId && currentFilters.startDate) {
            query = query.gte('transaction_date', currentFilters.startDate);
        }
        if (!currentFilters.monthId && currentFilters.endDate) {
            query = query.lte('transaction_date', currentFilters.endDate);
        }

        // تطبيق فلترة البحث إن وجدت
        if (currentFilters.searchTerm) {
            query = query.ilike('description', `%${currentFilters.searchTerm}%`);
        }

        const { data, error } = await query;

        if (error) throw error;

        // إعادة تعيين الإحصائيات
        bankStatistics.totalDeposits = 0;
        bankStatistics.totalWithdrawals = 0;
        bankStatistics.depositsCount = 0;
        bankStatistics.withdrawalsCount = 0;

        // حساب الإجماليات
        if (data && data.length > 0) {
            data.forEach(tx => {
                const amount = parseFloat(tx.amount) || 0;

                if (tx.transaction_type === 'deposit' || tx.transaction_type === 'transfer_in' || tx.transaction_type === 'initial_balance' || tx.transaction_type === 'إيداع' || tx.transaction_type === 'استرداد') {
                    bankStatistics.totalDeposits += amount;
                    bankStatistics.depositsCount++;
                } else if (tx.transaction_type === 'withdrawal' || tx.transaction_type === 'transfer_out' || tx.transaction_type === 'سحب' || tx.transaction_type === 'مصروفات') {
                    bankStatistics.totalWithdrawals += amount;
                    bankStatistics.withdrawalsCount++;
                }
            });
        }

        // حساب الصافي والنسبة المئوية
        bankStatistics.netBalance = bankStatistics.totalDeposits - bankStatistics.totalWithdrawals;

        // حساب النسبة المئوية (الصافي بالنسبة للإيداعات)
        if (bankStatistics.totalDeposits > 0) {
            bankStatistics.balancePercentage = Math.round((bankStatistics.netBalance / bankStatistics.totalDeposits) * 100);
        } else {
            bankStatistics.balancePercentage = 0;
        }

        // تحديث بطاقات الإحصائيات
        updateStatisticsCards();

    } catch (error) {
        console.error('Error fetching bank statistics:', error);
        // في حالة الخطأ، عرض إحصائيات فارغة
        updateStatisticsCards(true);
    }
};

// --- دالة تحديث بطاقات الإحصائيات ---
const updateStatisticsCards = (error = false) => {
    // تحديث عناصر الواجهة بالإحصائيات المحسوبة
    if (totalDepositsElement) {
        totalDepositsElement.textContent = error ? 'خطأ' : bankStatistics.totalDeposits.toFixed(2) + ' ريال';
    }

    if (totalWithdrawalsElement) {
        totalWithdrawalsElement.textContent = error ? 'خطأ' : bankStatistics.totalWithdrawals.toFixed(2) + ' ريال';
    }

    if (netBalanceElement) {
        netBalanceElement.textContent = error ? 'خطأ' : bankStatistics.netBalance.toFixed(2) + ' ريال';

        // إضافة لون للصافي حسب القيمة (إيجابي أو سلبي)
        if (!error) {
            netBalanceElement.classList.remove('positive-amount', 'negative-amount');
            if (bankStatistics.netBalance > 0) {
                netBalanceElement.classList.add('positive-amount');
            } else if (bankStatistics.netBalance < 0) {
                netBalanceElement.classList.add('negative-amount');
            }
        }
    }

    if (depositsCountElement) {
        depositsCountElement.textContent = error ? '(0 معاملة)' : `(${bankStatistics.depositsCount} معاملة)`;
    }

    if (withdrawalsCountElement) {
        withdrawalsCountElement.textContent = error ? '(0 معاملة)' : `(${bankStatistics.withdrawalsCount} معاملة)`;
    }

    if (balancePercentageElement) {
        // عرض النسبة المئوية مع علامة موجب/سالب
        const prefix = bankStatistics.balancePercentage > 0 ? '+' : '';
        balancePercentageElement.textContent = error ? '0%' : `${prefix}${bankStatistics.balancePercentage}%`;

        // تلوين النسبة حسب القيمة
        if (!error) {
            balancePercentageElement.classList.remove('positive-amount', 'negative-amount');
            if (bankStatistics.balancePercentage > 0) {
                balancePercentageElement.classList.add('positive-amount');
            } else if (bankStatistics.balancePercentage < 0) {
                balancePercentageElement.classList.add('negative-amount');
            }
        }
    }
};

// --- UI Population ---

// New: Populate Year Filter Dropdown (Modified to populate both)
const populateYearFilter = () => {
    // Populate Filter dropdown
    if (filterYearSelect) {
        const firstFilterOption = filterYearSelect.options[0]; // Keep "All Years"
        filterYearSelect.innerHTML = '';
        if (firstFilterOption) filterYearSelect.appendChild(firstFilterOption);

        availableYears.forEach(year => {
            const option = document.createElement('option');
            option.value = year.id;
            option.textContent = year.year_number;
            filterYearSelect.appendChild(option);
        });
        filterYearSelect.value = currentFilters.yearId || '';
        if (currentFilters.yearId) {
            fetchBudgetMonthsForYear(currentFilters.yearId);
        } else {
            if(filterMonthSelect) {
                 filterMonthSelect.innerHTML = '<option value="">كل الشهور</option>';
                 filterMonthSelect.disabled = true;
            }
        }
    }

    // Populate Modal dropdown
    if (transactionYearSelect) {
        const firstModalOption = transactionYearSelect.options[0]; // Keep "-- Select Year --"
        transactionYearSelect.innerHTML = '';
        if (firstModalOption) transactionYearSelect.appendChild(firstModalOption);

        availableYears.forEach(year => {
            const option = document.createElement('option');
            option.value = year.id;
            option.textContent = year.year_number;
            transactionYearSelect.appendChild(option);
        });
        // Don't pre-select here, handle in openTransactionModal
    }
};

// New: Populate Month Filter Dropdown (Filter only)
const populateMonthFilter = () => {
    if (!filterMonthSelect) return;
    const firstOption = filterMonthSelect.options[0]; // Keep "All Months"
    filterMonthSelect.innerHTML = '';
    if (firstOption) filterMonthSelect.appendChild(firstOption);

    availableMonths.forEach(month => {
        const option = document.createElement('option');
        option.value = month.id;
        option.textContent = getMonthName(month.month_number); // Use helper function
        filterMonthSelect.appendChild(option);
    });
    filterMonthSelect.value = currentFilters.monthId || '';
    filterMonthSelect.disabled = !currentFilters.yearId; // Disable if no year selected
};

// New: Populate Month Filter Dropdown (Modal only)
const populateModalMonthFilter = () => {
    if (!transactionMonthSelect) return;
    const firstOption = transactionMonthSelect.options[0]; // Keep "-- Select Month --"
    transactionMonthSelect.innerHTML = '';
    if (firstOption) transactionMonthSelect.appendChild(firstOption);

    modalAvailableMonths.forEach(month => {
        const option = document.createElement('option');
        option.value = month.id;
        option.textContent = getMonthName(month.month_number); // Use helper function
        transactionMonthSelect.appendChild(option);
    });
    // Don't pre-select here, handle in openTransactionModal
    transactionMonthSelect.disabled = !transactionYearSelect?.value; // Disable if no year selected in modal
};

// Populates the filter dropdown above the table
const populateBankFilter = () => {
    if (!filterBankSelect) return;
    const firstOption = filterBankSelect.options[0]; // Keep "All Banks"
    filterBankSelect.innerHTML = '';
    if (firstOption) filterBankSelect.appendChild(firstOption);

    // Use the globally filtered availableBanks
    availableBanks.forEach(bank => {
        const option = document.createElement('option');
        option.value = bank.id;
        option.textContent = bank.name;
        filterBankSelect.appendChild(option);
    });
    // Set the dropdown value based on current filter state
    filterBankSelect.value = currentFilters.bankId || '';
};

// Populates the bank dropdown inside the Add/Edit modal
const populateBankDropdown = () => {
    if (!bankIdSelect) return; // Modal bank select
    const firstOption = bankIdSelect.options[0]; // Keep placeholder
    bankIdSelect.innerHTML = '';
    if (firstOption) bankIdSelect.appendChild(firstOption);

    // Use the globally filtered availableBanks
    availableBanks.forEach(bank => {
        const option = document.createElement('option');
        option.value = bank.id;
        option.textContent = bank.name;
        bankIdSelect.appendChild(option);
    });
};

// New function to populate the sidebar list
const populateBanksSidebar = () => {
    if (!banksSidebarList) {
        console.error("Sidebar list element (#banks-sidebar-list) not found.");
        return;
    }
    banksSidebarList.innerHTML = ''; // Clear existing items

    console.log(`Populating sidebar with ${availableBanks.length} banks.`); // Log before populating

    // Add "All Banks" option first
    const allLi = document.createElement('li');
    const allA = document.createElement('a');
    allA.href = '#'; // Prevent page reload
    allA.dataset.bankId = 'all';
    allA.textContent = 'الكل';
    allA.classList.add('all-banks-option');
    allA.addEventListener('click', (e) => {
        e.preventDefault();
        console.log('All banks option clicked'); // Log click
        // Remove active class from other links
        banksSidebarList.querySelectorAll('a.active').forEach(el => el.classList.remove('active'));
        // Add active class to clicked link
        allA.classList.add('active');
        // Clear bank filter to show all banks of the type
        currentFilters.bankId = '';
        filterBankSelect.value = ''; // Sync filter dropdown
        currentPage = 1; // Reset page
        fetchTransactions();
        fetchBankStatistics();
    });
    allLi.appendChild(allA);
    banksSidebarList.appendChild(allLi);

    if (availableBanks.length === 0) {
        // Display a more informative message if fetching failed or no banks exist
        const messageLi = document.createElement('li');
        messageLi.innerHTML = `<span class="loading-message">لا توجد بنوك (${selectedBankType || 'غير محدد'}).</span>`;
        banksSidebarList.appendChild(messageLi);
        return;
    }

    availableBanks.forEach(bank => {
        const li = document.createElement('li');
        const a = document.createElement('a');
        a.href = '#'; // Prevent page reload
        a.dataset.bankId = bank.id;
        a.textContent = bank.name;
        a.addEventListener('click', (e) => {
            e.preventDefault();
            console.log(`Sidebar link clicked for bank ID: ${bank.id}`); // Log click
            // Remove active class from other links
            banksSidebarList.querySelectorAll('a.active').forEach(el => el.classList.remove('active'));
            // Add active class to clicked link
            a.classList.add('active');
            // Set current filter and fetch transactions
            currentFilters.bankId = bank.id;
            filterBankSelect.value = bank.id; // Sync filter dropdown
            currentPage = 1; // Reset page
            fetchTransactions();
            fetchBankStatistics();
        });
        li.appendChild(a);
        banksSidebarList.appendChild(li);
    });
};

const renderTransactionsTable = async () => {
    transactionsTableBody.innerHTML = '';
    if (!currentTransactions || currentTransactions.length === 0) {
        // Message is handled by fetchTransactions if count is 0
        return;
    }

    // Get all deposit transactions and their related withdrawals
    const depositIds = currentTransactions
        .filter(tx => tx.transaction_type === 'deposit')
        .map(tx => tx.id);

    let paymentsMap = {};
    if (depositIds.length > 0) {
        try {
            const { data: payments, error } = await _supabase
                .from('bank_transactions')
                .select('reference_id, amount, transaction_date, description, banks(name)')
                .eq('transaction_type', 'withdrawal')
                .in('reference_id', depositIds)
                .order('transaction_date', { ascending: false });

            if (!error && payments) {
                paymentsMap = payments.reduce((acc, payment) => {
                    if (!acc[payment.reference_id]) {
                        acc[payment.reference_id] = {
                            totalPaid: 0,
                            count: 0,
                            payments: []
                        };
                    }
                    acc[payment.reference_id].totalPaid += parseFloat(payment.amount);
                    acc[payment.reference_id].count += 1;
                    acc[payment.reference_id].payments.push(payment);
                    return acc;
                }, {});
            }
        } catch (error) {
            console.error('Error fetching payment data:', error);
        }
    }

    // Process transactions to show calculated results
    const processedTransactions = [];

    currentTransactions.forEach(tx => {
        if (tx.transaction_type === 'deposit') {
            const paymentData = paymentsMap[tx.id];
            if (paymentData) {
                const remainingAmount = parseFloat(tx.amount) - paymentData.totalPaid;

                if (remainingAmount === 0) {
                    // Show original deposit with strikethrough (fully paid)
                    processedTransactions.push({
                        ...tx,
                        displayType: 'fully_paid_deposit',
                        displayAmount: tx.amount,
                        originalAmount: tx.amount,
                        totalPaid: paymentData.totalPaid,
                        paymentCount: paymentData.count,
                        payments: paymentData.payments
                    });
                } else if (remainingAmount > 0) {
                    // Show remaining amount as deposit
                    processedTransactions.push({
                        ...tx,
                        displayType: 'partial_deposit',
                        displayAmount: remainingAmount,
                        originalAmount: tx.amount,
                        totalPaid: paymentData.totalPaid,
                        paymentCount: paymentData.count,
                        payments: paymentData.payments
                    });
                } else {
                    // Show excess amount as withdrawal (negative remaining)
                    processedTransactions.push({
                        ...tx,
                        displayType: 'excess_withdrawal',
                        displayAmount: Math.abs(remainingAmount),
                        originalAmount: tx.amount,
                        totalPaid: paymentData.totalPaid,
                        paymentCount: paymentData.count,
                        payments: paymentData.payments
                    });
                }
            } else {
                // Original deposit with no payments
                processedTransactions.push({
                    ...tx,
                    displayType: 'original_deposit',
                    displayAmount: tx.amount,
                    originalAmount: tx.amount,
                    totalPaid: 0,
                    paymentCount: 0,
                    payments: []
                });
            }
        } else {
            // Non-deposit transactions (withdrawals, transfers, etc.) - show as is
            // But exclude withdrawals that are linked to deposits (they're already calculated above)
            if (!(tx.transaction_type === 'withdrawal' && tx.reference_table === 'bank_transactions')) {
                processedTransactions.push({
                    ...tx,
                    displayType: 'original',
                    displayAmount: tx.amount,
                    originalAmount: tx.amount,
                    totalPaid: 0,
                    paymentCount: 0,
                    payments: []
                });
            }
        }
    });

    // Render processed transactions
    processedTransactions.forEach((tx, index) => {
        const row = document.createElement('tr');
        const sequenceNumber = (currentPage - 1) * itemsPerPage + index + 1;

        // Determine display properties based on display type
        let amountClass = '';
        let amountPrefix = '';
        let transactionType = tx.transaction_type;
        let rowClass = '';
        let detailsButton = '';

        switch (tx.displayType) {
            case 'fully_paid_deposit':
                amountClass = 'amount-positive fully-paid';
                amountPrefix = '+';
                transactionType = 'deposit';
                rowClass = 'fully-paid-row';
                detailsButton = `<button class="details-btn" data-transaction-id="${tx.id}" title="عرض تفاصيل الحسابات">
                    <i class="fas fa-calculator"></i>
                </button>`;
                break;
            case 'partial_deposit':
                amountClass = 'amount-positive partial-paid';
                amountPrefix = '+';
                transactionType = 'deposit';
                rowClass = 'partial-paid-row';
                detailsButton = `<button class="details-btn" data-transaction-id="${tx.id}" title="عرض تفاصيل الحسابات">
                    <i class="fas fa-calculator"></i>
                </button>`;
                break;
            case 'excess_withdrawal':
                amountClass = 'amount-negative excess-paid';
                amountPrefix = '-';
                transactionType = 'withdrawal';
                rowClass = 'excess-paid-row';
                detailsButton = `<button class="details-btn" data-transaction-id="${tx.id}" title="عرض تفاصيل الحسابات">
                    <i class="fas fa-calculator"></i>
                </button>`;
                break;
            case 'original_deposit':
                amountClass = 'amount-positive';
                amountPrefix = '+';
                transactionType = 'deposit';
                break;
            default:
                if (tx.transaction_type === 'deposit' || tx.transaction_type === 'transfer_in' || tx.transaction_type === 'initial_balance' || tx.transaction_type === 'إيداع') {
                    amountClass = 'amount-positive';
                    amountPrefix = '+';
                } else if (tx.transaction_type === 'withdrawal' || tx.transaction_type === 'transfer_out' || tx.transaction_type === 'سحب' || tx.transaction_type === 'مصروفات') {
                    amountClass = 'amount-negative';
                    amountPrefix = '-';
                } else {
                    // Default for unknown types
                    amountClass = 'amount-positive';
                    amountPrefix = '+';
                }
                break;
        }

        // Check if the transaction is linked
        const isLinked = !!tx.reference_table;

        row.className = rowClass;
        row.innerHTML = `
            <td>${sequenceNumber}</td>
            <td>${tx.banks?.name || 'N/A'}</td>
            <td>${formatDate(tx.transaction_date)}</td>
            <td>${translateTransactionType(transactionType)}</td>
            <td class="${amountClass}">
                ${amountPrefix}${formatCurrency(tx.displayAmount)}
                ${tx.paymentCount > 0 ? `<span class="payment-indicator">(${tx.paymentCount} دفعات)</span>` : ''}
            </td>
            <td>${tx.description || '-'}</td>
            <td>${tx.reference_table ? `${tx.reference_table} (${tx.reference_id})` : '-'}</td>
            <td>
                ${detailsButton}
                <button class="action-btn edit-btn" data-id="${tx.id}" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" data-id="${tx.id}" title="حذف">
                    <i class="fas fa-trash-alt"></i>
                </button>
                ${isLinked ? '<span class="tooltip-icon" title="هذه معاملة مرتبطة بسجلات أخرى"><i class="fas fa-info-circle"></i></span>' : ''}
            </td>
        `;

        // Add event listeners for edit/delete/details
        const editBtn = row.querySelector('.edit-btn');
        const deleteBtn = row.querySelector('.delete-btn');
        const detailsBtn = row.querySelector('.details-btn');

        if (editBtn) {
            editBtn.addEventListener('click', () => openTransactionModal(tx));
        }

        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => handleDeleteTransaction(tx.id));
        }

        if (detailsBtn) {
            detailsBtn.addEventListener('click', () => {
                const transactionId = detailsBtn.dataset.transactionId;
                showCalculationDetails(transactionId);
            });
        }

        transactionsTableBody.appendChild(row);
    });
};

const renderPaginationControls = () => {
    if (!paginationControls || totalItems <= itemsPerPage) {
        if (paginationControls) paginationControls.innerHTML = '';
        return;
    }

    paginationControls.innerHTML = '';
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    const prevButton = document.createElement('button');
    prevButton.innerHTML = '&laquo; السابق';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            fetchTransactions();
        }
    });
    paginationControls.appendChild(prevButton);

    const pageInfo = document.createElement('span');
    pageInfo.textContent = ` صفحة ${currentPage} من ${totalPages} `;
    pageInfo.style.margin = '0 10px';
    paginationControls.appendChild(pageInfo);

    const nextButton = document.createElement('button');
    nextButton.innerHTML = 'التالي &raquo;';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            fetchTransactions();
        }
    });
    paginationControls.appendChild(nextButton);
};

// --- Filtering Logic ---
const applyFilters = () => {
    currentFilters.yearId = filterYearSelect.value; // New
    currentFilters.monthId = filterMonthSelect.value; // New
    currentFilters.bankId = filterBankSelect.value; // Get bank from filter dropdown
    currentFilters.type = filterTypeSelect.value;
    // Disable date range if month is selected
    if (currentFilters.monthId) {
        currentFilters.startDate = '';
        currentFilters.endDate = '';
        filterStartDateInput.value = ''; // Clear input visually
        filterEndDateInput.value = '';   // Clear input visually
        filterStartDateInput.disabled = true; // Disable date inputs
        filterEndDateInput.disabled = true;
    } else {
        currentFilters.startDate = filterStartDateInput.value;
        currentFilters.endDate = filterEndDateInput.value;
        filterStartDateInput.disabled = false; // Enable date inputs
        filterEndDateInput.disabled = false;
    }
    currentFilters.searchTerm = searchInput.value.trim();
    currentPage = 1; // Reset to first page when filters change

    // Update sidebar active state based on dropdown
    banksSidebarList.querySelectorAll('a.active').forEach(el => el.classList.remove('active'));
    if (currentFilters.bankId) {
        const activeLink = banksSidebarList.querySelector(`a[data-bank-id="${currentFilters.bankId}"]`);
        if (activeLink) activeLink.classList.add('active');
    }

    fetchTransactions();
    // تحديث الإحصائيات عند تطبيق الفلاتر
    fetchBankStatistics();
};

const resetFilters = () => {
    filterYearSelect.value = '';   // New
    filterMonthSelect.value = '';  // New
    filterMonthSelect.disabled = true; // New: Disable month dropdown
    filterBankSelect.value = '';
    filterTypeSelect.value = '';
    filterStartDateInput.value = '';
    filterEndDateInput.value = '';
    filterStartDateInput.disabled = false; // New: Enable date inputs
    filterEndDateInput.disabled = false;   // New: Enable date inputs
    searchInput.value = '';
    currentFilters = { yearId: '', monthId: '', bankId: '', type: '', startDate: '', endDate: '', searchTerm: '' }; // Reset all filters
    currentPage = 1;
    // Remove active state from sidebar
    banksSidebarList.querySelectorAll('a.active').forEach(el => el.classList.remove('active'));
    fetchTransactions(); // Fetch transactions for all banks of the selected type
    // تحديث الإحصائيات عند إعادة تعيين الفلاتر
    fetchBankStatistics();
};

// --- Modal Logic ---
const openTransactionModal = async (transactionToEdit = null) => { // Make async
    resetTransactionForm();
    populateBankDropdown(); // Ensure bank dropdown has the correct banks for the type
    populateYearFilter(); // Ensure year dropdown is populated in modal

    if (transactionToEdit) {
        // Editing existing transaction
        transactionFormTitle.textContent = 'تعديل معاملة بنكية';
        transactionIdField.value = transactionToEdit.id;
        bankIdSelect.value = transactionToEdit.bank_id;
        transactionDateInput.value = formatDate(transactionToEdit.transaction_date);
        transactionTypeSelect.value = transactionToEdit.transaction_type;
        amountInput.value = transactionToEdit.amount;
        descriptionInput.value = transactionToEdit.description || '';

        // --- New: Fetch month/year for editing ---
        if (transactionToEdit.budget_month_id) {
            try {
                // Fetch the month to get the year_id
                const { data: monthData, error: monthError } = await _supabase
                    .from('budget_months')
                    .select('id, budget_year_id')
                    .eq('id', transactionToEdit.budget_month_id)
                    .single();

                if (monthError) throw monthError;

                if (monthData && monthData.budget_year_id) {
                    transactionYearSelect.value = monthData.budget_year_id;
                    // Fetch months for the selected year and then set the month value
                    await fetchModalMonthsForYear(monthData.budget_year_id); // Wait for months to load
                    transactionMonthSelect.value = transactionToEdit.budget_month_id; // Now set the month
                } else {
                     console.warn("Could not find year for the transaction's month.");
                     transactionMonthSelect.disabled = true;
                }
            } catch (error) {
                console.error("Error fetching month/year details for edit:", error);
                showMessage(transactionFormMessage, 'خطأ في تحميل تفاصيل الشهر/السنة للمعاملة.', 'warning', 0);
                transactionMonthSelect.disabled = true;
            }
        } else {
            // No budget month associated
            transactionMonthSelect.disabled = true;
        }
        // --- End New ---

        // For linked transactions, show a warning but don't disable fields
        if (transactionToEdit.reference_table) {
            showMessage(transactionFormMessage, 'تحذير: هذه معاملة مرتبطة بسجلات أخرى. تعديلها قد يسبب عدم تطابق في البيانات.', 'warning', 0);
        }
    } else {
        // Adding new transaction
        transactionFormTitle.textContent = 'إضافة معاملة بنكية يدوية';
        transactionIdField.value = '';
        transactionDateInput.value = formatDate(new Date()); // Default to today

        // --- New: Pre-select year/month based on current session or filters ---
        const currentYearMonth = getCurrentYearAndMonth();

        // أولوية للسنة والشهر الحالي من sessionStorage
        if (currentYearMonth.yearNumber && currentYearMonth.monthNumber) {
            const yearId = getYearIdFromYearNumber(currentYearMonth.yearNumber);
            if (yearId) {
                transactionYearSelect.value = yearId;
                await fetchModalMonthsForYear(yearId);

                // البحث عن معرف الشهر من رقم الشهر
                const monthId = getMonthIdFromMonthNumber(currentYearMonth.monthNumber, yearId);
                if (monthId) {
                    transactionMonthSelect.value = monthId;
                }
                transactionMonthSelect.disabled = false;
            } else {
                // إذا لم نجد السنة، استخدم الفلاتر الحالية
                if (currentFilters.yearId) {
                    transactionYearSelect.value = currentFilters.yearId;
                    await fetchModalMonthsForYear(currentFilters.yearId);
                    if (currentFilters.monthId) {
                        transactionMonthSelect.value = currentFilters.monthId;
                    }
                    transactionMonthSelect.disabled = false;
                } else {
                    transactionMonthSelect.disabled = true;
                }
            }
        } else if (currentFilters.yearId) {
            // استخدم الفلاتر الحالية إذا لم تكن هناك بيانات في sessionStorage
            transactionYearSelect.value = currentFilters.yearId;
            await fetchModalMonthsForYear(currentFilters.yearId);
            if (currentFilters.monthId) {
                transactionMonthSelect.value = currentFilters.monthId;
            }
            transactionMonthSelect.disabled = false;
        } else {
            transactionMonthSelect.disabled = true;
        }
        // --- End New ---

        // Pre-select bank if one is active in the filter/sidebar
        if (currentFilters.bankId) {
            bankIdSelect.value = currentFilters.bankId;
        }
    }
    transactionModal.classList.add('show');
    document.body.style.overflow = 'hidden';
};

const closeTransactionModal = () => {
    transactionModal.classList.remove('show');
    document.body.style.overflow = '';
    resetTransactionForm();
};

const resetTransactionForm = () => {
    if (transactionForm) transactionForm.reset();
    transactionIdField.value = '';
    referenceTableInput.value = ''; // Ensure reference fields are cleared
    referenceIdInput.value = '';
    // Reset new dropdowns
    if (transactionYearSelect) transactionYearSelect.value = '';
    if (transactionMonthSelect) {
        transactionMonthSelect.value = '';
        transactionMonthSelect.innerHTML = '<option value="">-- اختر الشهر --</option>'; // Reset options
        transactionMonthSelect.disabled = true;
    }
    bankIdSelect.disabled = false;
    transactionDateInput.disabled = false;
    transactionTypeSelect.disabled = false;
    amountInput.disabled = false;
    if (transactionFormMessage) transactionFormMessage.style.display = 'none';
};

const handleTransactionSubmit = async (event) => {
    event.preventDefault();
    const submitBtn = transactionForm.querySelector('.submit-btn');
    submitBtn.disabled = true;
    submitBtn.textContent = 'جاري الحفظ...';
    showMessage(transactionFormMessage, 'جاري حفظ المعاملة...', 'info', 0);

    // --- New: Get budget_month_id ---
    const selectedMonthId = transactionMonthSelect.value || null; // Use null if empty
    // --- End New ---

    const transactionData = {
        bank_id: bankIdSelect.value,
        transaction_date: transactionDateInput.value,
        transaction_type: transactionTypeSelect.value,
        amount: parseFloat(amountInput.value) || 0,
        description: descriptionInput.value.trim() || null,
        budget_month_id: selectedMonthId // Add selected month ID
    };

    const transactionId = transactionIdField.value;

    try {
        // Validation
        if (!transactionData.bank_id) throw new Error('البنك مطلوب.');
        // Check if selected bank belongs to the current page's bank type
        const selectedBank = availableBanks.find(b => b.id === transactionData.bank_id);
        if (!selectedBank) {
            throw new Error(`البنك المحدد غير صالح لهذا النوع (${selectedBankType}).`);
        }

        if (!transactionData.transaction_date) throw new Error('تاريخ المعاملة مطلوب.');
        if (!transactionData.transaction_type) throw new Error('نوع المعاملة مطلوب.');
        if (transactionData.amount <= 0) throw new Error('المبلغ يجب أن يكون أكبر من صفر.');
        // New Validation: Month is required if Year is selected
        if (transactionYearSelect.value && !transactionData.budget_month_id) {
            throw new Error('الشهر المالي مطلوب عند اختيار سنة مالية.');
        }

        let result;
        if (transactionId) {
            // Update - removed the is('reference_table', null) condition to allow updating any transaction
            const { data, error } = await _supabase
                .from('bank_transactions')
                .update(transactionData)
                .eq('id', transactionId)
                .select()
                .single();

            if (error) throw error;
            if (!data) throw new Error('فشل تحديث المعاملة.');
            result = data;
            showMessage(transactionFormMessage, 'تم تحديث المعاملة بنجاح!', 'success');
        } else {
            // Insert
            const { data, error } = await _supabase
                .from('bank_transactions')
                .insert([transactionData])
                .select()
                .single();
            if (error) throw error;
            result = data;
            showMessage(transactionFormMessage, 'تم إضافة المعاملة بنجاح!', 'success');
        }

        console.log("Transaction saved:", result);
        // Refresh transactions only if the saved transaction's bank/month matches the current filter
        const matchesBankFilter = !currentFilters.bankId || currentFilters.bankId === result.bank_id;
        const matchesMonthFilter = !currentFilters.monthId || currentFilters.monthId === result.budget_month_id;
        // Add year filter check if needed
        const matchesYearFilter = !currentFilters.yearId || transactionYearSelect.value === currentFilters.yearId; // Check if saved year matches filter year

        if (matchesBankFilter && matchesMonthFilter && matchesYearFilter) {
             fetchTransactions(); // Refresh table
             fetchBankStatistics(); // Refresh stats
        }
        setTimeout(closeTransactionModal, 1500);

    } catch (error) {
        console.error('Error saving transaction:', error);
        showMessage(transactionFormMessage, `خطأ في الحفظ: ${error.message}`, 'error', 0);
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = transactionId ? 'تحديث المعاملة' : 'حفظ المعاملة';
    }
};

// --- Add Bank Modal Functions ---
const openAddBankModal = () => {
    if (!addBankModal) return;

    // Set the bank_type field to the current bank type
    if (bankTypeField) {
        bankTypeField.value = selectedBankType;
    }

    // Show the modal
    addBankModal.classList.add('show');
    document.body.style.overflow = 'hidden';

    // Focus on the first field
    setTimeout(() => {
        document.getElementById('bank_name')?.focus();
    }, 100);
};

const closeAddBankModal = () => {
    if (!addBankModal) return;
    addBankModal.classList.remove('show');
    document.body.style.overflow = '';

    // Reset the form
    if (addBankForm) addBankForm.reset();
    if (addBankFormMessage) addBankFormMessage.style.display = 'none';
};

// Handle add bank form submission
const handleAddBankSubmit = async (event) => {
    event.preventDefault();
    if (!addBankForm || !addBankFormMessage) return;

    const submitBtn = addBankForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.disabled = true;

    showMessage(addBankFormMessage, 'جاري حفظ البنك...', 'info', 0);

    // Get form data
    const formData = new FormData(addBankForm);
    const bankData = {};
    formData.forEach((value, key) => {
        bankData[key] = value.trim() || null;
    });

    // Validate required fields
    if (!bankData.bank_name) {
        showMessage(addBankFormMessage, 'اسم البنك مطلوب', 'error');
        if (submitBtn) submitBtn.disabled = false;
        return;
    }

    try {
        // Insert the new bank
        const { data, error } = await _supabase
            .from('banks')
            .insert([
                {
                    name: bankData.bank_name,
                    account_number: bankData.account_number,
                    branch: bankData.branch,
                    contact_phone: bankData.contact_phone,
                    notes: bankData.notes,
                    bank_type: bankData.bank_type
                }
            ])
            .select();

        if (error) {
            console.error('Error adding bank:', error);
            showMessage(addBankFormMessage, `خطأ في حفظ البنك: ${error.message}`, 'error');
        } else {
            console.log('Bank added successfully:', data);
            showMessage(addBankFormMessage, 'تم إضافة البنك بنجاح', 'success');

            // Refresh banks list
            setTimeout(() => {
                fetchBanks(selectedBankType);
                closeAddBankModal();
            }, 1500);
        }
    } catch (error) {
        console.error('Error adding bank:', error);
        showMessage(addBankFormMessage, `خطأ غير متوقع: ${error.message}`, 'error');
    } finally {
        if (submitBtn) submitBtn.disabled = false;
    }
};

// --- Delete Handling ---
const handleDeleteTransaction = async (transactionId) => {
    // Stronger confirmation prompt warning about linked transactions
    const confirmation = confirm(
        'تحذير: هل أنت متأكد من حذف هذه المعاملة البنكية؟\n\n' +
        'إذا كانت هذه المعاملة مرتبطة بسجل آخر (مثل راتب أو مصروف)، فقد يؤدي حذفها إلى عدم تطابق في البيانات.\n\n' +
        'لا يمكن التراجع عن هذا الإجراء.'
    );
    if (!confirmation) return; // Stop if user cancels

    showMessage(listMessage, 'جاري حذف المعاملة...', 'info', 0); // Show processing message

    try {
        // First, get the transaction details to handle UI updates after deletion
        const { data: txData, error: fetchError } = await _supabase
            .from('bank_transactions')
            .select('bank_id')
            .eq('id', transactionId)
            .single();

        if (fetchError) {
            throw new Error(`فشل في العثور على المعاملة: ${fetchError.message}`);
        }

        // Now, delete the transaction regardless of reference_table value
        const { error } = await _supabase
            .from('bank_transactions')
            .delete()
            .eq('id', transactionId);

        if (error) throw error;

        // Deletion successful
        showMessage(listMessage, 'تم حذف المعاملة بنجاح.', 'success');

        // Refresh transactions if the deleted transaction's bank matches the current filter
        // or if we're not filtering by bank
        if (!currentFilters.bankId || (txData && currentFilters.bankId === txData.bank_id)) {
            fetchTransactions(); // Refresh the table
        }
    } catch (error) {
        console.error('Error deleting transaction:', error);
        let userMessage = `خطأ في الحذف: ${error.message}`;
        // Check for specific constraint errors
        if (error.message.includes('constraint') || error.code === '23503') {
            userMessage = 'خطأ: لا يمكن حذف المعاملة لوجود قيود مرتبطة بها في قاعدة البيانات.';
        }
        showMessage(listMessage, userMessage, 'error', 0);
    }
};

// --- Event Listeners Setup ---
const setupEventListeners = () => {
    // Filters
    if (filterBtn) filterBtn.addEventListener('click', applyFilters);
    if (resetFilterBtn) resetFilterBtn.addEventListener('click', resetFilters);
    if (searchInput) searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') applyFilters();
    });

    // *** New: Year dropdown listener ***
    if (filterYearSelect) {
        filterYearSelect.addEventListener('change', () => {
            const selectedYear = filterYearSelect.value;
            currentFilters.yearId = selectedYear; // Update state immediately
            currentFilters.monthId = ''; // Reset month when year changes
            fetchBudgetMonthsForYear(selectedYear); // Fetch months for the new year
            // Optionally apply filters immediately or wait for month selection/apply button
             applyFilters(); // Apply filters right away when year changes
        });
    }
    // *** End New ***

    // Apply filters on dropdown change for Month, Bank, Type
    if (filterMonthSelect) filterMonthSelect.addEventListener('change', applyFilters); // Apply when month changes
    if (filterBankSelect) filterBankSelect.addEventListener('change', applyFilters);
    if (filterTypeSelect) filterTypeSelect.addEventListener('change', applyFilters);
    // Apply filters on date change potentially (only if month is not selected)
    if (filterStartDateInput) filterStartDateInput.addEventListener('change', () => {
        if (!filterMonthSelect.value) applyFilters();
    });
    if (filterEndDateInput) filterEndDateInput.addEventListener('change', () => {
        if (!filterMonthSelect.value) applyFilters();
    });


    // Add Button
    const addTransactionBtn = document.getElementById('add-transaction-btn');
    if (addTransactionBtn) {
        addTransactionBtn.addEventListener('click', () => openTransactionModal());
    } else {
        console.warn("Add Transaction button (#add-transaction-btn) not found.");
    }


    // Modal Listeners (Transaction Modal)
    if (closeTransactionModalBtn) closeTransactionModalBtn.addEventListener('click', closeTransactionModal);
    if (transactionModal) transactionModal.addEventListener('click', (e) => {
        if (e.target === transactionModal) closeTransactionModal();
    });
    if (transactionForm) transactionForm.addEventListener('submit', handleTransactionSubmit);

    // *** New: Year dropdown listener (Modal) ***
    if (transactionYearSelect) {
        transactionYearSelect.addEventListener('change', () => {
            const selectedYear = transactionYearSelect.value;
            transactionMonthSelect.value = ''; // Reset month when year changes in modal
            fetchModalMonthsForYear(selectedYear); // Fetch months for the modal dropdown
        });
    }
    // *** End New ***

    // Add Bank Modal Listeners
    if (addBankBtn) {
        addBankBtn.addEventListener('click', openAddBankModal);
    }

    if (closeAddBankModalBtn) {
        closeAddBankModalBtn.addEventListener('click', closeAddBankModal);
    }

    if (addBankModal) {
        addBankModal.addEventListener('click', (e) => {
            if (e.target === addBankModal) {
                closeAddBankModal();
            }
        });
    }

    if (addBankForm) {
        addBankForm.addEventListener('submit', handleAddBankSubmit);
    }

    // Sidebar Back Button
    if (backToFinanceBtnSidebar) {
        backToFinanceBtnSidebar.addEventListener('click', () => {
            window.location.href = '../financial_dashboard.html'; // Adjust path if needed
        });
    }

    // Switch Bank Type Button
    const switchBankTypeBtn = document.getElementById('switch-bank-type-btn');
    if (switchBankTypeBtn) {
        switchBankTypeBtn.addEventListener('click', () => {
            // تحديد النوع الجديد بناءً على النوع الحالي
            const newType = selectedBankType === 'مركزي' ? 'آجل' : 'مركزي';

            // الانتقال إلى الصفحة الجديدة
            window.location.href = `bank_transactions.html?type=${encodeURIComponent(newType)}`;
        });
    }

    // Global Escape Listener (updated to handle both modals)
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            if (transactionModal?.classList.contains('show')) {
                closeTransactionModal();
            }
            if (addBankModal?.classList.contains('show')) {
                closeAddBankModal();
            }
        }
    });

    // Sidebar bank link listeners are added in populateBanksSidebar
    // Table action listeners are added during table rendering
};

// تحديد نمط الصفحة بناءً على نوع البنك
const applyBankTypeStyle = (bankType) => {
    const bodyElement = document.body;

    // إزالة جميع فئات نوع البنك السابقة
    bodyElement.classList.remove('bank-type-markazi', 'bank-type-ajel');

    // إضافة الفئة المناسبة حسب نوع البنك
    if (bankType === 'مركزي') {
        bodyElement.classList.add('bank-type-markazi');
    } else if (bankType === 'اجل') {
        bodyElement.classList.add('bank-type-ajel');
    }

    // تحديث أيقونات الناف بار والعناوين
    if (bankType === 'مركزي') {
        // أيقونة البنك المركزي (مبنى بنك)
        const iconHtml = '<i class="fas fa-university"></i>';
        if (navbarTitle) {
            navbarTitle.innerHTML = `${iconHtml} المعاملات البنكية (المركزية)`;
        }
    } else if (bankType === 'اجل') {
        // أيقونة البنك الآجل (تقويم/موعد)
        const iconHtml = '<i class="fas fa-calendar-alt"></i>';
        if (navbarTitle) {
            navbarTitle.innerHTML = `${iconHtml} المعاملات البنكية (الآجلة)`;
        }
    }
};

// --- Update UI Titles ---
const updateTitles = (bankType) => {
    // تحديث عرض العناوين مع الحفاظ على الشكل المعروض للمستخدم
    const typeText = bankType === 'مركزي' ? 'المركزية' : 'الآجلة'; // العرض للمستخدم مع الألف
    const fullTitle = `المعاملات البنكية (${typeText})`;
    const sidebarHeaderText = `البنوك ${typeText}`;

    // تطبيق نمط التصميم المناسب حسب نوع البنك
    applyBankTypeStyle(bankType);

    if (navbarTitle) navbarTitle.textContent = fullTitle;
    if (sidebarTitle) sidebarTitle.textContent = sidebarHeaderText;
    if (mainHeaderTitle) {
        // تخصيص أيقونة العنوان حسب نوع البنك
        const iconClass = bankType === 'مركزي' ? 'fas fa-university' : 'fas fa-calendar-alt';
        mainHeaderTitle.innerHTML = `<i class="${iconClass}"></i> ${fullTitle}`;
    }

    // تحديث نص زر التبديل
    updateSwitchButtonText(bankType);
};

// دالة تحديث نص زر التبديل
const updateSwitchButtonText = (currentBankType) => {
    const switchBtnText = document.getElementById('switch-btn-text');
    if (switchBtnText) {
        if (currentBankType === 'مركزي') {
            switchBtnText.textContent = 'البنوك الآجلة';
        } else {
            switchBtnText.textContent = 'البنوك المركزية';
        }
    }
};

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('Bank Transactions DOM loaded.');

    selectedBankType = getBankTypeFromURL(); // Get type from URL

    if (selectedBankType) {
        updateTitles(selectedBankType); // Update titles and apply styles based on type
        setupEventListeners(); // Setup listeners early
        await fetchActiveYears(); // Fetch years first (populates both filter & modal)
        await fetchBanks(selectedBankType); // Fetch banks filtered by type

        // Populate the sidebar with banks
        populateBanksSidebar();

        // Set "All" as default selection in sidebar
        setTimeout(() => {
            const allOption = banksSidebarList.querySelector('a[data-bank-id="all"]');
            if (allOption) {
                allOption.classList.add('active');
            }
        }, 100);

        // Set default filters (no bank filter to show all banks of the type)
        currentFilters.bankId = ''; // Clear bank filter to show all banks of the type

        // تعيين السنة والشهر الحالي من sessionStorage في مربع التصفية
        const currentYearMonth = getCurrentYearAndMonth();
        if (currentYearMonth.yearNumber && currentYearMonth.monthNumber) {
            const yearId = getYearIdFromYearNumber(currentYearMonth.yearNumber);
            if (yearId) {
                currentFilters.yearId = yearId;
                if (filterYearSelect) filterYearSelect.value = yearId;

                // جلب الأشهر للسنة المحددة
                await fetchBudgetMonthsForYear(yearId);

                // البحث عن معرف الشهر وتعيينه
                const monthId = getMonthIdFromMonthNumber(currentYearMonth.monthNumber, yearId);
                if (monthId) {
                    currentFilters.monthId = monthId;
                    if (filterMonthSelect) filterMonthSelect.value = monthId;
                }
            }
        }

        // Fetch initial data for all banks of the type
        await fetchTransactions();
        await fetchBankStatistics();

        // Show/hide pay due button based on bank type
        updatePayDueButtonVisibility();
    } else {
        // Handle case where type is missing or invalid (message shown in getBankTypeFromURL)
        console.error("Selected bank type is invalid or missing.");
        // Disable controls or redirect if necessary
        if (filterBtn) filterBtn.disabled = true;
        if (resetFilterBtn) resetFilterBtn.disabled = true;
        const addBtn = document.getElementById('add-transaction-btn');
        if (addBtn) addBtn.disabled = true;
        if (banksSidebarList) banksSidebarList.innerHTML = `<li class="loading-message error-message">خطأ: نوع البنك غير محدد.</li>`; // Show error in sidebar
    }
});

// --- Pay Due Functions ---

// Update pay due button visibility based on bank type
const updatePayDueButtonVisibility = () => {
    if (payDueBtn) {
        // Show pay due button only for deferred banks (آجل)
        if (selectedBankType === 'اجل') {
            payDueBtn.style.display = 'block';
        } else {
            payDueBtn.style.display = 'none';
        }
    }
};

// Fetch deferred banks for step 1
const fetchDeferredBanks = async () => {
    try {
        const { data, error } = await _supabase
            .from('banks')
            .select('id, name, account_number')
            .eq('bank_type', 'اجل')
            .order('name', { ascending: true });

        if (error) throw error;

        deferredBanks = data || [];
        renderDeferredBanksList();

    } catch (error) {
        console.error('Error fetching deferred banks:', error);
        showMessage(payDueMessage, `خطأ في جلب البنوك الآجلة: ${error.message}`, 'error');
    }
};

// Fetch central banks for step 3
const fetchCentralBanks = async () => {
    try {
        const { data, error } = await _supabase
            .from('banks')
            .select('id, name')
            .eq('bank_type', 'مركزي')
            .order('name', { ascending: true });

        if (error) throw error;

        centralBanks = data || [];
        populateCentralBanksSelect();

    } catch (error) {
        console.error('Error fetching central banks:', error);
        showMessage(payDueMessage, `خطأ في جلب البنوك المركزية: ${error.message}`, 'error');
    }
};

// Fetch deposit transactions for selected deferred bank with payment calculations
const fetchDepositTransactions = async (bankId) => {
    try {
        // First, get all deposit transactions
        const { data: deposits, error: depositsError } = await _supabase
            .from('bank_transactions')
            .select('id, transaction_date, amount, description')
            .eq('bank_id', bankId)
            .eq('transaction_type', 'deposit')
            .order('transaction_date', { ascending: false });

        if (depositsError) throw depositsError;

        // Then, get all withdrawal transactions that reference these deposits
        const depositIds = deposits.map(d => d.id);
        let withdrawals = [];

        if (depositIds.length > 0) {
            const { data: withdrawalData, error: withdrawalError } = await _supabase
                .from('bank_transactions')
                .select('id, amount, reference_id')
                .eq('bank_id', bankId)
                .eq('transaction_type', 'withdrawal')
                .in('reference_id', depositIds);

            if (withdrawalError) throw withdrawalError;
            withdrawals = withdrawalData || [];
        }

        // Calculate paid amounts for each deposit
        const depositsWithPayments = deposits.map(deposit => {
            const relatedPayments = withdrawals.filter(w => w.reference_id == deposit.id);
            const totalPaid = relatedPayments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
            const remainingAmount = parseFloat(deposit.amount) - totalPaid;

            return {
                ...deposit,
                totalPaid,
                remainingAmount,
                paymentCount: relatedPayments.length,
                payments: relatedPayments
            };
        });

        renderDepositTransactionsList(depositsWithPayments);

    } catch (error) {
        console.error('Error fetching deposit transactions:', error);
        showMessage(payDueMessage, `خطأ في جلب معاملات الإيداع: ${error.message}`, 'error');
    }
};

// Render deferred banks list
const renderDeferredBanksList = () => {
    if (!deferredBanksList) return;

    if (deferredBanks.length === 0) {
        deferredBanksList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-university"></i>
                <h5>لا توجد بنوك آجلة</h5>
                <p>لا توجد بنوك آجلة مسجلة في النظام</p>
            </div>
        `;
        return;
    }

    deferredBanksList.innerHTML = deferredBanks.map(bank => `
        <div class="bank-item" data-bank-id="${bank.id}">
            <h5>${bank.name}</h5>
            <p>رقم الحساب: ${bank.account_number || 'غير محدد'}</p>
        </div>
    `).join('');

    // Add click event listeners
    deferredBanksList.querySelectorAll('.bank-item').forEach(item => {
        item.addEventListener('click', () => {
            const bankId = item.dataset.bankId;
            const bank = deferredBanks.find(b => b.id == bankId);
            if (bank) {
                selectDeferredBank(bank);
            }
        });
    });
};

// Render deposit transactions list
const renderDepositTransactionsList = (transactions) => {
    if (!depositTransactionsList) return;

    if (transactions.length === 0) {
        depositTransactionsList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-arrow-down"></i>
                <h5>لا توجد معاملات إيداع</h5>
                <p>لا توجد معاملات إيداع لهذا البنك</p>
            </div>
        `;
        return;
    }

    depositTransactionsList.innerHTML = transactions.map(transaction => {
        const isFullyPaid = transaction.remainingAmount <= 0;
        const hasPayments = transaction.paymentCount > 0;

        // Determine button text and functionality
        let buttonHtml = '';
        if (isFullyPaid) {
            buttonHtml = `
                <div class="payment-status fully-paid">
                    <i class="fas fa-check-circle"></i> مدفوع بالكامل
                </div>
            `;
        } else if (hasPayments) {
            buttonHtml = `
                <div class="payment-actions">
                    <button class="pay-transaction-btn" data-transaction-id="${transaction.id}" data-amount="${transaction.remainingAmount}">
                        إضافة دفعة أخرى
                    </button>
                    <div class="payment-counter" data-transaction-id="${transaction.id}">
                        <i class="fas fa-history"></i> ${transaction.paymentCount}
                    </div>
                </div>
            `;
        } else {
            buttonHtml = `
                <button class="pay-transaction-btn" data-transaction-id="${transaction.id}" data-amount="${transaction.remainingAmount}">
                    دفع
                </button>
            `;
        }

        return `
            <div class="transaction-item ${isFullyPaid ? 'fully-paid' : hasPayments ? 'partially-paid' : 'unpaid'}" data-transaction-id="${transaction.id}">
                <div class="transaction-info">
                    <h6>إيداع بتاريخ ${formatDate(transaction.transaction_date)}</h6>
                    <p>${transaction.description || 'بدون وصف'}</p>
                    <div class="payment-breakdown">
                        <span class="original-amount">المبلغ الأصلي: ${formatCurrency(transaction.amount)} ريال</span>
                        ${hasPayments ? `<span class="paid-amount">المدفوع: ${formatCurrency(transaction.totalPaid)} ريال</span>` : ''}
                        ${!isFullyPaid ? `<span class="remaining-amount">المتبقي: ${formatCurrency(transaction.remainingAmount)} ريال</span>` : ''}
                    </div>
                </div>
                <div class="transaction-actions">
                    <div class="transaction-amount clickable-amount" data-transaction-id="${transaction.id}">
                        ${formatCurrency(transaction.remainingAmount)} ريال
                    </div>
                    ${buttonHtml}
                </div>
            </div>
        `;
    }).join('');

    // Add click event listeners for pay buttons
    depositTransactionsList.querySelectorAll('.pay-transaction-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const transactionId = btn.dataset.transactionId;
            const amount = btn.dataset.amount;
            const transaction = transactions.find(t => t.id == transactionId);
            if (transaction) {
                selectTransaction(transaction);
            }
        });
    });

    // Add click event listeners for payment counters
    depositTransactionsList.querySelectorAll('.payment-counter').forEach(counter => {
        counter.addEventListener('click', () => {
            const transactionId = counter.dataset.transactionId;
            showPaymentHistory(transactionId);
        });
    });

    // Add click event listeners for clickable amounts
    depositTransactionsList.querySelectorAll('.clickable-amount').forEach(amount => {
        amount.addEventListener('click', () => {
            const transactionId = amount.dataset.transactionId;
            showTransactionDetails(transactionId);
        });
    });
};

// Populate central banks select
const populateCentralBanksSelect = () => {
    if (!centralBankSelect) return;

    centralBankSelect.innerHTML = '<option value="">اختر البنك المركزي</option>';

    centralBanks.forEach(bank => {
        const option = document.createElement('option');
        option.value = bank.id;
        option.textContent = bank.name;
        centralBankSelect.appendChild(option);
    });
};

// Select deferred bank and move to step 2
const selectDeferredBank = (bank) => {
    selectedDeferredBank = bank;

    // Update UI
    if (selectedBankName) selectedBankName.textContent = `معاملات الإيداع - ${bank.name}`;
    if (deferredBankName) deferredBankName.textContent = bank.name;

    // Fetch deposit transactions
    fetchDepositTransactions(bank.id);

    // Move to step 2
    showStep(2);
};

// Select transaction and move to step 3
const selectTransaction = (transaction) => {
    selectedTransaction = transaction;

    // Update UI with payment details
    const originalAmount = document.getElementById('original-amount');
    const paidAmount = document.getElementById('paid-amount');
    const dueAmount = document.getElementById('due-amount');
    const maxAmount = document.getElementById('max-amount');
    const paymentAmountInput = document.getElementById('payment-amount');

    if (originalAmount) originalAmount.textContent = `${formatCurrency(transaction.amount)} ريال`;
    if (paidAmount) paidAmount.textContent = `${formatCurrency(transaction.totalPaid || 0)} ريال`;
    if (dueAmount) dueAmount.textContent = `${formatCurrency(transaction.remainingAmount || transaction.amount)} ريال`;
    if (maxAmount) maxAmount.textContent = formatCurrency(transaction.remainingAmount || transaction.amount);

    // Set max value for payment amount input
    if (paymentAmountInput) {
        paymentAmountInput.max = transaction.remainingAmount || transaction.amount;
        paymentAmountInput.value = transaction.remainingAmount || transaction.amount; // Default to full remaining amount
    }

    // Set default payment date to today
    if (paymentDate) paymentDate.value = new Date().toISOString().split('T')[0];

    // Move to step 3
    showStep(3);
};

// Show specific step
const showStep = (stepNumber) => {
    currentStep = stepNumber;

    // Hide all steps
    [step1, step2, step3].forEach(step => {
        if (step) step.classList.remove('active');
    });

    // Show current step
    const currentStepElement = document.getElementById(`step-${stepNumber}`);
    if (currentStepElement) currentStepElement.classList.add('active');
};

// Open pay due modal
const openPayDueModal = () => {
    if (!payDueModal) return;

    // Reset state
    selectedDeferredBank = null;
    selectedTransaction = null;
    currentStep = 1;

    // Show modal
    payDueModal.classList.add('show');
    payDueModal.style.display = 'flex';

    // Show step 1
    showStep(1);

    // Fetch data
    fetchDeferredBanks();
    fetchCentralBanks();

    // Clear any previous messages
    if (payDueMessage) payDueMessage.style.display = 'none';
};

// Close pay due modal
const closePayDueModalFunc = () => {
    if (!payDueModal) return;

    payDueModal.classList.remove('show');
    payDueModal.style.display = 'none';

    // Reset form
    if (paymentForm) paymentForm.reset();

    // Clear messages
    if (payDueMessage) payDueMessage.style.display = 'none';
};

// Handle payment form submission
const handlePaymentSubmission = async (e) => {
    e.preventDefault();

    if (!selectedDeferredBank || !selectedTransaction) {
        showMessage(payDueMessage, 'خطأ: لم يتم اختيار البنك أو المعاملة', 'error');
        return;
    }

    const formData = new FormData(paymentForm);
    const centralBankId = formData.get('central-bank-select') || centralBankSelect.value;
    const paymentDateValue = formData.get('payment-date') || paymentDate.value;
    const paymentDetails = formData.get('payment-details') || document.getElementById('payment-details').value;
    const paymentAmountValue = parseFloat(formData.get('payment-amount') || document.getElementById('payment-amount').value);

    if (!centralBankId) {
        showMessage(payDueMessage, 'الرجاء اختيار البنك المركزي', 'error');
        return;
    }

    if (!paymentDateValue) {
        showMessage(payDueMessage, 'الرجاء تحديد تاريخ الدفع', 'error');
        return;
    }

    if (!paymentAmountValue || paymentAmountValue <= 0) {
        showMessage(payDueMessage, 'الرجاء تحديد مبلغ صحيح للدفع', 'error');
        return;
    }

    const maxAllowedAmount = selectedTransaction.remainingAmount || selectedTransaction.amount;
    if (paymentAmountValue > maxAllowedAmount) {
        showMessage(payDueMessage, `المبلغ المدخل أكبر من المبلغ المتبقي (${formatCurrency(maxAllowedAmount)} ريال)`, 'error');
        return;
    }

    try {
        // Create payment transaction in central bank
        const { data: paymentTransaction, error: paymentError } = await _supabase
            .from('bank_transactions')
            .insert({
                bank_id: centralBankId,
                transaction_type: 'withdrawal',
                amount: paymentAmountValue,
                transaction_date: paymentDateValue,
                description: `دفع مستحق للبنك الآجل: ${selectedDeferredBank.name} - ${paymentDetails || 'دفع مستحق'}`,
                reference_table: 'bank_transactions',
                reference_id: selectedTransaction.id
            })
            .select()
            .single();

        if (paymentError) throw paymentError;

        // Create reverse transaction in deferred bank (withdrawal)
        const { data: reverseTransaction, error: reverseError } = await _supabase
            .from('bank_transactions')
            .insert({
                bank_id: selectedDeferredBank.id,
                transaction_type: 'withdrawal',
                amount: paymentAmountValue,
                transaction_date: paymentDateValue,
                description: `دفع من البنك المركزي - ${paymentDetails || 'دفع مستحق'}`,
                reference_table: 'bank_transactions',
                reference_id: paymentTransaction.id
            })
            .select()
            .single();

        if (reverseError) throw reverseError;

        // Save payment details to deferred_payments table
        const { data: deferredPayment, error: deferredPaymentError } = await _supabase
            .from('deferred_payments')
            .insert({
                deferred_bank_id: selectedDeferredBank.id,
                central_bank_id: centralBankId,
                original_transaction_id: selectedTransaction.id,
                payment_amount: paymentAmountValue,
                payment_date: paymentDateValue,
                payment_details: paymentDetails || 'دفع مستحق',
                central_bank_transaction_id: paymentTransaction.id,
                deferred_bank_transaction_id: reverseTransaction.id,
                status: 'completed'
            })
            .select()
            .single();

        if (deferredPaymentError) {
            console.error('Error saving to deferred_payments:', deferredPaymentError);
            // Don't throw error here as the main transactions were successful
            // Just log the error for debugging
        } else {
            console.log('Deferred payment saved successfully:', deferredPayment);
        }

        showMessage(payDueMessage, `تم تسجيل الدفع بنجاح (${formatCurrency(paymentAmountValue)} ريال)`, 'success');

        // Close modal after success
        setTimeout(() => {
            closePayDueModalFunc();
            // Refresh data if we're viewing the affected bank type
            if (selectedBankType === 'مركزي' || selectedBankType === 'اجل') {
                fetchTransactions();
                fetchBankStatistics();
            }
        }, 2000);

    } catch (error) {
        console.error('Error processing payment:', error);
        showMessage(payDueMessage, `خطأ في معالجة الدفع: ${error.message}`, 'error');
    }
};

// Show payment history for a transaction
const showPaymentHistory = async (transactionId) => {
    try {
        const { data, error } = await _supabase
            .from('bank_transactions')
            .select('id, amount, transaction_date, description, banks(name)')
            .eq('reference_id', transactionId)
            .eq('transaction_type', 'withdrawal')
            .order('transaction_date', { ascending: false });

        if (error) throw error;

        const payments = data || [];
        let historyHtml = `
            <div class="payment-history-modal">
                <div class="modal-header">
                    <h4>تاريخ الدفعات</h4>
                    <button onclick="closePaymentHistory()" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
        `;

        if (payments.length === 0) {
            historyHtml += '<p>لا توجد دفعات مسجلة لهذه المعاملة</p>';
        } else {
            historyHtml += '<div class="payments-list">';
            payments.forEach((payment, index) => {
                historyHtml += `
                    <div class="payment-item">
                        <div class="payment-info">
                            <span class="payment-number">دفعة #${index + 1}</span>
                            <span class="payment-date">${formatDate(payment.transaction_date)}</span>
                        </div>
                        <div class="payment-amount">${formatCurrency(payment.amount)} ريال</div>
                        <div class="payment-bank">${payment.banks?.name || 'غير محدد'}</div>
                    </div>
                `;
            });
            historyHtml += '</div>';
        }

        historyHtml += `
                </div>
            </div>
            <div class="modal-overlay" onclick="closePaymentHistory()"></div>
        `;

        // Create and show modal
        const modalContainer = document.createElement('div');
        modalContainer.id = 'payment-history-container';
        modalContainer.innerHTML = historyHtml;
        document.body.appendChild(modalContainer);

    } catch (error) {
        console.error('Error fetching payment history:', error);
        alert(`خطأ في جلب تاريخ الدفعات: ${error.message}`);
    }
};

// Close payment history modal
const closePaymentHistory = () => {
    const container = document.getElementById('payment-history-container');
    if (container) {
        container.remove();
    }
};

// Show transaction details (original deposit + payments)
const showTransactionDetails = async (transactionId) => {
    try {
        // Get original transaction
        const { data: originalTransaction, error: originalError } = await _supabase
            .from('bank_transactions')
            .select('id, amount, transaction_date, description, banks(name)')
            .eq('id', transactionId)
            .single();

        if (originalError) throw originalError;

        // Get related payments
        const { data: payments, error: paymentsError } = await _supabase
            .from('bank_transactions')
            .select('id, amount, transaction_date, description, banks(name)')
            .eq('reference_id', transactionId)
            .eq('transaction_type', 'withdrawal')
            .order('transaction_date', { ascending: false });

        if (paymentsError) throw paymentsError;

        const totalPaid = payments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
        const remainingAmount = parseFloat(originalTransaction.amount) - totalPaid;

        let detailsHtml = `
            <div class="transaction-details-modal">
                <div class="modal-header">
                    <h4>تفاصيل المعاملة</h4>
                    <button onclick="closeTransactionDetails()" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="original-transaction">
                        <h5>المعاملة الأصلية (إيداع)</h5>
                        <div class="transaction-card">
                            <div class="transaction-info">
                                <span>التاريخ: ${formatDate(originalTransaction.transaction_date)}</span>
                                <span>البنك: ${originalTransaction.banks?.name || 'غير محدد'}</span>
                                <span>الوصف: ${originalTransaction.description || 'بدون وصف'}</span>
                            </div>
                            <div class="transaction-amount">${formatCurrency(originalTransaction.amount)} ريال</div>
                        </div>
                    </div>

                    <div class="payments-section">
                        <h5>الدفعات المسجلة (${payments.length})</h5>
        `;

        if (payments.length === 0) {
            detailsHtml += '<p>لا توجد دفعات مسجلة لهذه المعاملة</p>';
        } else {
            detailsHtml += '<div class="payments-list">';
            payments.forEach((payment, index) => {
                detailsHtml += `
                    <div class="payment-card">
                        <div class="payment-info">
                            <span>دفعة #${index + 1} - ${formatDate(payment.transaction_date)}</span>
                            <span>البنك: ${payment.banks?.name || 'غير محدد'}</span>
                            <span>الوصف: ${payment.description || 'بدون وصف'}</span>
                        </div>
                        <div class="payment-amount">${formatCurrency(payment.amount)} ريال</div>
                    </div>
                `;
            });
            detailsHtml += '</div>';
        }

        detailsHtml += `
                    </div>

                    <div class="summary-section">
                        <div class="summary-item">
                            <span>المبلغ الأصلي:</span>
                            <span>${formatCurrency(originalTransaction.amount)} ريال</span>
                        </div>
                        <div class="summary-item">
                            <span>إجمالي المدفوع:</span>
                            <span>${formatCurrency(totalPaid)} ريال</span>
                        </div>
                        <div class="summary-item highlight">
                            <span>المبلغ المتبقي:</span>
                            <span>${formatCurrency(remainingAmount)} ريال</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-overlay" onclick="closeTransactionDetails()"></div>
        `;

        // Create and show modal
        const modalContainer = document.createElement('div');
        modalContainer.id = 'transaction-details-container';
        modalContainer.innerHTML = detailsHtml;
        document.body.appendChild(modalContainer);

    } catch (error) {
        console.error('Error fetching transaction details:', error);
        alert(`خطأ في جلب تفاصيل المعاملة: ${error.message}`);
    }
};

// Close transaction details modal
const closeTransactionDetails = () => {
    const container = document.getElementById('transaction-details-container');
    if (container) {
        container.remove();
    }
};

// Show calculation details for processed transactions
const showCalculationDetails = async (originalTransactionId) => {
    try {
        // Get original transaction
        const { data: originalTransaction, error: originalError } = await _supabase
            .from('bank_transactions')
            .select('id, amount, transaction_date, description, banks(name)')
            .eq('id', originalTransactionId)
            .single();

        if (originalError) throw originalError;

        // Get related payments (withdrawals)
        const { data: payments, error: paymentsError } = await _supabase
            .from('bank_transactions')
            .select('id, amount, transaction_date, description, banks(name)')
            .eq('reference_id', originalTransactionId)
            .eq('transaction_type', 'withdrawal')
            .order('transaction_date', { ascending: false });

        if (paymentsError) throw paymentsError;

        const totalPaid = payments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
        const remainingAmount = parseFloat(originalTransaction.amount) - totalPaid;

        // Determine result type
        let resultType = '';
        let resultClass = '';
        let resultIcon = '';

        if (remainingAmount === 0) {
            resultType = 'مدفوع بالكامل';
            resultClass = 'fully-paid';
            resultIcon = 'fas fa-check-circle';
        } else if (remainingAmount > 0) {
            resultType = `متبقي للدفع: ${formatCurrency(remainingAmount)} ريال`;
            resultClass = 'partially-paid';
            resultIcon = 'fas fa-clock';
        } else {
            resultType = `زيادة في الدفع: ${formatCurrency(Math.abs(remainingAmount))} ريال`;
            resultClass = 'excess-paid';
            resultIcon = 'fas fa-exclamation-triangle';
        }

        let detailsHtml = `
            <div class="calculation-details-modal">
                <div class="modal-header">
                    <h4><i class="fas fa-calculator"></i> تفاصيل حسابات المعاملة</h4>
                    <button onclick="closeCalculationDetails()" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <!-- Original Transaction -->
                    <div class="calculation-section">
                        <h5><i class="fas fa-arrow-down"></i> المعاملة الأصلية (إيداع)</h5>
                        <div class="calculation-card original">
                            <div class="transaction-info">
                                <span><strong>التاريخ:</strong> ${formatDate(originalTransaction.transaction_date)}</span>
                                <span><strong>البنك:</strong> ${originalTransaction.banks?.name || 'غير محدد'}</span>
                                <span><strong>الوصف:</strong> ${originalTransaction.description || 'بدون وصف'}</span>
                            </div>
                            <div class="amount-display positive">
                                +${formatCurrency(originalTransaction.amount)} ريال
                            </div>
                        </div>
                    </div>

                    <!-- Payments Section -->
                    <div class="calculation-section">
                        <h5><i class="fas fa-arrow-up"></i> عمليات السحب المرتبطة (${payments.length})</h5>
        `;

        if (payments.length === 0) {
            detailsHtml += '<p class="no-payments">لا توجد عمليات سحب مرتبطة بهذه المعاملة</p>';
        } else {
            payments.forEach((payment, index) => {
                detailsHtml += `
                    <div class="calculation-card payment">
                        <div class="payment-number">عملية سحب #${index + 1}</div>
                        <div class="transaction-info">
                            <span><strong>التاريخ:</strong> ${formatDate(payment.transaction_date)}</span>
                            <span><strong>البنك:</strong> ${payment.banks?.name || 'غير محدد'}</span>
                            <span><strong>الوصف:</strong> ${payment.description || 'بدون وصف'}</span>
                        </div>
                        <div class="amount-display negative">
                            -${formatCurrency(payment.amount)} ريال
                        </div>
                    </div>
                `;
            });
        }

        detailsHtml += `
                    </div>

                    <!-- Calculation Summary -->
                    <div class="calculation-summary">
                        <h5><i class="fas fa-equals"></i> ملخص الحسابات</h5>
                        <div class="calculation-steps">
                            <div class="calculation-step">
                                <span>المبلغ الأصلي:</span>
                                <span class="positive">+${formatCurrency(originalTransaction.amount)} ريال</span>
                            </div>
                            <div class="calculation-step">
                                <span>إجمالي المسحوب:</span>
                                <span class="negative">-${formatCurrency(totalPaid)} ريال</span>
                            </div>
                            <div class="calculation-divider"></div>
                            <div class="calculation-result ${resultClass}">
                                <span><i class="${resultIcon}"></i> النتيجة:</span>
                                <span>${resultType}</span>
                            </div>
                        </div>

                        <!-- Mathematical Formula -->
                        <div class="formula-display">
                            <div class="formula">
                                ${formatCurrency(originalTransaction.amount)} - ${formatCurrency(totalPaid)} =
                                <span class="${remainingAmount >= 0 ? 'positive' : 'negative'}">
                                    ${remainingAmount >= 0 ? '+' : ''}${formatCurrency(remainingAmount)}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-overlay" onclick="closeCalculationDetails()"></div>
        `;

        // Create and show modal
        const modalContainer = document.createElement('div');
        modalContainer.id = 'calculation-details-container';
        modalContainer.innerHTML = detailsHtml;
        document.body.appendChild(modalContainer);

    } catch (error) {
        console.error('Error fetching calculation details:', error);
        alert(`خطأ في جلب تفاصيل الحسابات: ${error.message}`);
    }
};

// Close calculation details modal
const closeCalculationDetails = () => {
    const container = document.getElementById('calculation-details-container');
    if (container) {
        container.remove();
    }
};

// Event Listeners for Pay Due Modal
if (payDueBtn) {
    payDueBtn.addEventListener('click', openPayDueModal);
}

if (closePayDueModal) {
    closePayDueModal.addEventListener('click', closePayDueModalFunc);
}

if (backToStep1) {
    backToStep1.addEventListener('click', () => showStep(1));
}

if (backToStep2) {
    backToStep2.addEventListener('click', () => showStep(2));
}

if (cancelPayment) {
    cancelPayment.addEventListener('click', closePayDueModalFunc);
}

if (paymentForm) {
    paymentForm.addEventListener('submit', handlePaymentSubmission);
}

// Close modal when clicking outside
if (payDueModal) {
    payDueModal.addEventListener('click', (e) => {
        if (e.target === payDueModal) {
            closePayDueModalFunc();
        }
    });
}
