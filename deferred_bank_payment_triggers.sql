-- ملف إنشاء triggers لدفع المستحق من البنوك الآجلة
-- هذا الملف ينشئ triggers لمعالجة دفعات البنوك الآجلة وإنشاء المعاملات العكسية

-- الخطوة 1: إنشاء trigger لمزامنة financial_transactions_log إلى bank_transactions
-- (هذا موجود بالفعل ولكن نتأكد من وجوده)

-- التحقق من وجود trigger المزامنة
DO $$
BEGIN
    -- التحقق من وجود trigger المزامنة الأساسي
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'financial_log_to_bank_transactions_trigger'
        AND event_object_table = 'financial_transactions_log'
    ) THEN
        RAISE NOTICE 'Creating financial_log_to_bank_transactions_trigger...';
        
        CREATE TRIGGER financial_log_to_bank_transactions_trigger
        AFTER INSERT OR UPDATE OR DELETE ON public.financial_transactions_log
        FOR EACH ROW EXECUTE FUNCTION public.sync_log_to_bank_transactions();
        
        RAISE NOTICE 'Created financial_log_to_bank_transactions_trigger successfully';
    ELSE
        RAISE NOTICE 'financial_log_to_bank_transactions_trigger already exists';
    END IF;
END $$;

-- الخطوة 2: إنشاء trigger لمعالجة المعاملات العكسية للبنوك الآجلة
-- هذا trigger يراقب المعاملات الجديدة في bank_transactions ويتحقق من إمكانية الإلغاء

CREATE OR REPLACE FUNCTION public.handle_deferred_bank_payment_cancellation()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_original_transaction RECORD;
    v_reverse_transaction RECORD;
    v_bank_type TEXT;
BEGIN
    -- التحقق من أن هذه معاملة سحب من بنك آجل
    IF TG_OP = 'INSERT' AND NEW.transaction_type = 'withdrawal' THEN
        
        -- التحقق من نوع البنك
        SELECT bank_type INTO v_bank_type
        FROM public.banks 
        WHERE id = NEW.bank_id;
        
        -- إذا كان البنك آجل ولديه reference_id
        IF v_bank_type = 'آجل' AND NEW.reference_id IS NOT NULL THEN
            
            -- البحث عن المعاملة الأصلية (الإيداع)
            SELECT * INTO v_original_transaction
            FROM public.bank_transactions
            WHERE id = NEW.reference_id::uuid
            AND bank_id = NEW.bank_id
            AND transaction_type = 'deposit';
            
            -- إذا وجدت المعاملة الأصلية
            IF FOUND THEN
                -- التحقق من تساوي المبالغ (مع هامش خطأ صغير)
                IF ABS(v_original_transaction.amount - NEW.amount) < 0.01 THEN
                    
                    RAISE NOTICE 'Found matching deposit and withdrawal transactions for bank %, amounts: % and %', 
                        NEW.bank_id, v_original_transaction.amount, NEW.amount;
                    
                    -- حذف المعاملة الأصلية (الإيداع)
                    DELETE FROM public.bank_transactions 
                    WHERE id = v_original_transaction.id;
                    
                    -- حذف المعاملة الحالية (السحب) - سيتم تنفيذه بعد انتهاء trigger
                    -- نستخدم متغير لتذكر أننا نريد حذف هذه المعاملة
                    
                    RAISE NOTICE 'Deleted original deposit transaction % and will delete reverse transaction %', 
                        v_original_transaction.id, NEW.id;
                    
                    -- إرجاع NULL لمنع إدراج المعاملة العكسية
                    RETURN NULL;
                END IF;
            END IF;
        END IF;
    END IF;
    
    -- إرجاع NEW للمعاملات العادية
    RETURN NEW;
END;
$$;

-- إنشاء trigger للمعاملات العكسية
DROP TRIGGER IF EXISTS deferred_bank_payment_cancellation_trigger ON public.bank_transactions;

CREATE TRIGGER deferred_bank_payment_cancellation_trigger
    BEFORE INSERT ON public.bank_transactions
    FOR EACH ROW 
    EXECUTE FUNCTION public.handle_deferred_bank_payment_cancellation();

-- الخطوة 3: إنشاء trigger إضافي لمعالجة الحالات المتأخرة
-- هذا trigger يعمل بعد الإدراج للتحقق من المعاملات المتطابقة

CREATE OR REPLACE FUNCTION public.cleanup_matching_deferred_transactions()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_matching_transactions RECORD;
    v_bank_type TEXT;
BEGIN
    -- التحقق من أن هذه معاملة في بنك آجل
    IF TG_OP = 'INSERT' THEN
        
        -- التحقق من نوع البنك
        SELECT bank_type INTO v_bank_type
        FROM public.banks 
        WHERE id = NEW.bank_id;
        
        -- إذا كان البنك آجل
        IF v_bank_type = 'آجل' THEN
            
            -- البحث عن المعاملات المتطابقة (إيداع وسحب بنفس المبلغ)
            FOR v_matching_transactions IN
                SELECT 
                    t1.id as deposit_id,
                    t1.amount as deposit_amount,
                    t2.id as withdrawal_id,
                    t2.amount as withdrawal_amount
                FROM public.bank_transactions t1
                JOIN public.bank_transactions t2 ON (
                    t1.bank_id = t2.bank_id 
                    AND t1.bank_id = NEW.bank_id
                    AND ABS(t1.amount - t2.amount) < 0.01
                    AND t1.transaction_type = 'deposit'
                    AND t2.transaction_type = 'withdrawal'
                    AND t1.id != t2.id
                )
                WHERE t1.bank_id = NEW.bank_id
                LIMIT 10 -- تحديد عدد المعاملات للمعالجة في كل مرة
            LOOP
                
                RAISE NOTICE 'Found matching transactions: deposit % (%) and withdrawal % (%) for bank %', 
                    v_matching_transactions.deposit_id, v_matching_transactions.deposit_amount,
                    v_matching_transactions.withdrawal_id, v_matching_transactions.withdrawal_amount,
                    NEW.bank_id;
                
                -- حذف المعاملات المتطابقة
                DELETE FROM public.bank_transactions 
                WHERE id IN (v_matching_transactions.deposit_id, v_matching_transactions.withdrawal_id);
                
                RAISE NOTICE 'Deleted matching transactions % and %', 
                    v_matching_transactions.deposit_id, v_matching_transactions.withdrawal_id;
                
            END LOOP;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;

-- إنشاء trigger للتنظيف
DROP TRIGGER IF EXISTS cleanup_matching_deferred_transactions_trigger ON public.bank_transactions;

CREATE TRIGGER cleanup_matching_deferred_transactions_trigger
    AFTER INSERT ON public.bank_transactions
    FOR EACH ROW 
    EXECUTE FUNCTION public.cleanup_matching_deferred_transactions();

-- الخطوة 4: إنشاء دالة للتحقق من حالة triggers
CREATE OR REPLACE FUNCTION public.check_deferred_payment_triggers_status()
RETURNS TABLE(
    trigger_name TEXT,
    table_name TEXT,
    trigger_exists BOOLEAN,
    function_exists BOOLEAN,
    status TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.trigger_name::TEXT,
        t.table_name::TEXT,
        EXISTS(
            SELECT 1 FROM information_schema.triggers tr
            WHERE tr.trigger_name = t.trigger_name
            AND tr.event_object_table = t.table_name
        ) as trigger_exists,
        EXISTS(
            SELECT 1 FROM information_schema.routines r
            WHERE r.routine_name = t.function_name
            AND r.routine_type = 'FUNCTION'
        ) as function_exists,
        CASE 
            WHEN EXISTS(SELECT 1 FROM information_schema.triggers tr WHERE tr.trigger_name = t.trigger_name AND tr.event_object_table = t.table_name)
            AND EXISTS(SELECT 1 FROM information_schema.routines r WHERE r.routine_name = t.function_name AND r.routine_type = 'FUNCTION')
            THEN 'نشط'
            ELSE 'غير نشط'
        END::TEXT as status
    FROM (VALUES
        ('financial_log_to_bank_transactions_trigger', 'financial_transactions_log', 'sync_log_to_bank_transactions'),
        ('deferred_bank_payment_cancellation_trigger', 'bank_transactions', 'handle_deferred_bank_payment_cancellation'),
        ('cleanup_matching_deferred_transactions_trigger', 'bank_transactions', 'cleanup_matching_deferred_transactions')
    ) AS t(trigger_name, table_name, function_name);
END;
$$;

-- الخطوة 5: اختبار triggers
-- عرض حالة triggers
SELECT '=== حالة Triggers دفع المستحق ===' as section;
SELECT * FROM public.check_deferred_payment_triggers_status();

-- رسالة نجاح
SELECT '=== تم إنشاء جميع triggers بنجاح ===' as result;
SELECT 'يمكنك الآن استخدام نظام دفع المستحق من البنوك الآجلة' as instructions;
