# شرح نظام التريجرات المالية في قاعدة البيانات

## 📋 نظرة عامة

يحتوي النظام على ثلاثة جداول رئيسية مترابطة عبر نظام تريجرات متقدم:

1. **`student_payments`** - جدول دفعات الطلاب
2. **`financial_transactions_log`** - سجل المعاملات المالية
3. **`bank_transactions`** - المعاملات البنكية

## 🏗️ هيكل الجداول

### جدول `student_payments`
```sql
- id (UUID) - معرف الدفعة
- student_id (UUID) - معرف الطالب
- budget_month_id (UUID) - معرف الشهر المالي
- bank_id (UUID) - معرف البنك
- amount (NUMERIC) - مبلغ الدفعة
- payment_date (DATE) - تاريخ الدفعة
- payment_status (TEXT) - حالة الدفعة
- created_at (TIMESTAMP) - تاريخ الإنشاء
```

### جدول `financial_transactions_log`
```sql
- id (UUID) - معرف السجل المالي
- transaction_date (DATE) - تاريخ المعاملة
- amount (NUMERIC) - المبلغ
- transaction_type (ENUM) - نوع المعاملة (إيداع/سحب)
- description (TEXT) - الوصف
- source_table (VARCHAR) - الجدول المصدر
- source_record_id (TEXT) - معرف السجل المصدر
- bank_id (UUID) - معرف البنك
- budget_month_id (UUID) - معرف الشهر المالي
- is_reversal (BOOLEAN) - هل هو سجل عكسي
- original_log_entry_id (UUID) - معرف السجل الأصلي (للسجلات العكسية)
- created_at (TIMESTAMP) - تاريخ الإنشاء
- updated_at (TIMESTAMP) - تاريخ التحديث
```

### جدول `bank_transactions`
```sql
- id (UUID) - معرف المعاملة البنكية
- bank_id (UUID) - معرف البنك
- amount (NUMERIC) - المبلغ
- transaction_type (ENUM) - نوع المعاملة
- transaction_date (DATE) - تاريخ المعاملة
- description (TEXT) - الوصف
- transaction_source (TEXT) - مصدر المعاملة
- reference_id (UUID) - معرف المرجع
- reference_table (TEXT) - جدول المرجع
- budget_month_id (UUID) - معرف الشهر المالي
- financial_transaction_log_id (UUID) - معرف السجل المالي المرتبط
- created_at (TIMESTAMP) - تاريخ الإنشاء
- updated_at (TIMESTAMP) - تاريخ التحديث
```

## 🔄 سير العمل

### 1. إضافة دفعة جديدة

```mermaid
graph TD
    A[إدراج دفعة في student_payments] --> B[تريجر handle_student_payment_changes_v2]
    B --> C[إنشاء سجل في financial_transactions_log]
    C --> D[تريجر sync_log_to_bank_transactions]
    D --> E[إنشاء معاملة في bank_transactions]
```

**الخطوات:**
1. يتم إدراج دفعة جديدة في `student_payments`
2. يتم تشغيل تريجر `handle_student_payment_changes_v2`
3. ينشئ التريجر سجل جديد في `financial_transactions_log` بنوع "إيداع"
4. يتم تشغيل تريجر `sync_log_to_bank_transactions`
5. ينشئ التريجر معاملة بنكية جديدة في `bank_transactions` مرتبطة بالسجل المالي

### 2. تعديل دفعة موجودة

```mermaid
graph TD
    A[تعديل دفعة في student_payments] --> B[تريجر handle_student_payment_changes_v2]
    B --> C[إنشاء سجل عكسي للقيمة القديمة]
    C --> D[تريجر sync_log_to_bank_transactions للسجل العكسي]
    D --> E[إلغاء ربط المعاملة البنكية]
    E --> F[إنشاء سجل جديد للقيمة الجديدة]
    F --> G[تريجر sync_log_to_bank_transactions للسجل الجديد]
    G --> H[ربط المعاملة البنكية بالسجل الجديد]
```

**الخطوات:**
1. يتم تعديل دفعة في `student_payments`
2. يتم تشغيل تريجر `handle_student_payment_changes_v2`
3. ينشئ التريجر سجل عكسي (سحب) للقيمة القديمة مع `is_reversal = true`
4. يتم تشغيل تريجر `sync_log_to_bank_transactions` للسجل العكسي
5. يقوم التريجر بإلغاء ربط المعاملة البنكية (`financial_transaction_log_id = NULL`)
6. ينشئ التريجر سجل جديد (إيداع) للقيمة الجديدة
7. يتم تشغيل تريجر `sync_log_to_bank_transactions` للسجل الجديد
8. يقوم التريجر بربط المعاملة البنكية بالسجل الجديد

### 3. حذف دفعة

```mermaid
graph TD
    A[حذف دفعة من student_payments] --> B[تريجر handle_student_payment_changes_v2]
    B --> C[إنشاء سجل عكسي للحذف]
    C --> D[تريجر sync_log_to_bank_transactions]
    D --> E[إلغاء ربط المعاملة البنكية]
    E --> F[تنظيف المعاملات غير المرتبطة]
```

**الخطوات:**
1. يتم حذف دفعة من `student_payments`
2. يتم تشغيل تريجر `handle_student_payment_changes_v2`
3. ينشئ التريجر سجل عكسي (سحب) للحذف مع `is_reversal = true`
4. يتم تشغيل تريجر `sync_log_to_bank_transactions`
5. يقوم التريجر بإلغاء ربط المعاملة البنكية
6. يتم تنظيف المعاملات البنكية غير المرتبطة

## 🛠️ التريجرات والدوال

### 1. `handle_student_payment_changes_v2()`

**الغرض:** معالجة تغييرات دفعات الطلاب وإرسالها إلى السجل المالي

**العمليات:**
- **INSERT:** إنشاء سجل مالي جديد
- **UPDATE:** إنشاء سجل عكسي + سجل جديد
- **DELETE:** إنشاء سجل عكسي للحذف

**المنطق:**
```sql
-- للإدراج: إنشاء سجل إيداع (بدون فحص التكرار)
INSERT INTO financial_transactions_log (
    transaction_type = 'إيداع',
    is_reversal = false,
    source_table = 'student_payments',
    source_record_id = NEW.id::text
)

-- للتعديل: سجل عكسي + سجل جديد
-- 1. إنشاء سجل عكسي للقيمة القديمة
INSERT INTO financial_transactions_log (
    transaction_type = 'سحب',
    is_reversal = true,
    original_log_entry_id = old_log_id,
    source_record_id = OLD.id::text
)
-- 2. إنشاء سجل جديد للقيمة الجديدة
INSERT INTO financial_transactions_log (
    transaction_type = 'إيداع',
    is_reversal = false,
    source_record_id = NEW.id::text
)

-- للحذف: سجل عكسي
INSERT INTO financial_transactions_log (
    transaction_type = 'سحب',
    is_reversal = true,
    original_log_entry_id = old_log_id,
    source_record_id = OLD.id::text
)
```

### 2. `sync_log_to_bank_transactions()`

**الغرض:** مزامنة السجلات المالية مع المعاملات البنكية

**العمليات:**
- **INSERT (سجل عكسي):** إلغاء ربط المعاملة البنكية
- **INSERT (سجل عادي):** إنشاء أو تحديث معاملة بنكية
- **UPDATE:** تحديث المعاملة البنكية المرتبطة
- **DELETE:** حذف المعاملة البنكية

**المنطق:**
```sql
-- للسجل العادي (غير العكسي): إنشاء أو تحديث معاملة بنكية
IF NEW.is_reversal = false THEN
    -- البحث عن معاملة بنكية موجودة بنفس reference_id
    SELECT id FROM bank_transactions
    WHERE reference_table = NEW.source_table
    AND reference_id = NEW.source_record_id::uuid;

    IF found THEN
        -- تحديث المعاملة الموجودة (في حالة التعديل)
        UPDATE bank_transactions SET
            amount = NEW.amount,
            transaction_type = NEW.transaction_type,
            financial_transaction_log_id = NEW.id,
            description = NEW.description;
    ELSE
        -- إنشاء معاملة جديدة (للدفعات الجديدة)
        INSERT INTO bank_transactions (
            bank_id, amount, transaction_type, reference_id,
            reference_table, financial_transaction_log_id, ...
        );
    END IF;

-- للسجل العكسي: إلغاء الربط فقط
ELSIF NEW.is_reversal = true THEN
    UPDATE bank_transactions
    SET financial_transaction_log_id = NULL
    WHERE financial_transaction_log_id = NEW.original_log_entry_id;
```

## 🔍 مثال عملي

### السيناريو: دفعات متعددة لنفس الطالب + تعديل إحداها

#### 1. الإدراج الأولي - الدفعة الأولى
```sql
INSERT INTO student_payments (amount = 9999, student_id = 'A', month_id = 'Jan', ...)
```

**النتيجة:**
- `financial_transactions_log`: سجل واحد (9999 إيداع)
- `bank_transactions`: معاملة واحدة (9999 إيداع) مرتبطة بالسجل المالي

#### 2. إضافة دفعة ثانية لنفس الطالب في نفس الشهر
```sql
INSERT INTO student_payments (amount = 5555, student_id = 'A', month_id = 'Jan', ...)
```

**النتيجة:**
- `financial_transactions_log`: سجل جديد (5555 إيداع)
- `bank_transactions`: معاملة جديدة منفصلة (5555 إيداع)
- **المجموع**: دفعتان منفصلتان لنفس الطالب في نفس الشهر

#### 3. تعديل الدفعة الأولى (من 9999 إلى 8888)
```sql
UPDATE student_payments SET amount = 8888 WHERE id = 'payment_1'
```

**النتيجة:**
- `financial_transactions_log`:
  - السجل الأصلي (9999 إيداع)
  - سجل عكسي (9999 سحب) يشير للأصلي
  - سجل جديد (8888 إيداع)
- `bank_transactions`:
  - **نفس المعاملة البنكية** للدفعة الأولى محدثة (8888 إيداع)
  - الدفعة الثانية تبقى كما هي (5555 إيداع)
  - **المجموع**: 8888 + 5555 = 14443 ريال

#### 4. الحذف
```sql
DELETE FROM student_payments WHERE id = 'payment_1'
```

**النتيجة:**
- `financial_transactions_log`:
  - جميع السجلات السابقة
  - سجل عكسي جديد (8888 سحب) للحذف
- `bank_transactions`:
  - المعاملة الأولى غير مرتبطة (`financial_transaction_log_id = NULL`)
  - الدفعة الثانية تبقى كما هي (5555 إيداع)
  - **المجموع**: 5555 ريال فقط

## 🎯 المبادئ الأساسية

### 1. **الشفافية المالية**
- كل تغيير يتم تسجيله في السجل المالي
- السجلات العكسية تحافظ على تاريخ التغييرات
- لا يتم حذف أي سجل مالي نهائياً

### 2. **الربط الذكي**
- كل دفعة طالب لها معاملة بنكية واحدة بالضبط
- المعاملات البنكية مرتبطة دائماً بآخر سجل مالي صالح لنفس الدفعة
- السجلات العكسية تلغي الربط مؤقتاً حتى يأتي السجل الجديد
- عند التعديل: نفس المعاملة البنكية تُحدث لتشير للسجل الجديد

### 3. **التنظيف التلقائي**
- المعاملات البنكية غير المرتبطة يتم تنظيفها
- النظام يحافظ على التوازن بين الجداول

## 🚨 نقاط مهمة

### 1. **ترتيب التنفيذ**
- السجل العكسي يتم إنشاؤه أولاً (يلغي الربط)
- السجل الجديد يتم إنشاؤه ثانياً (ينشئ ربط جديد)
- هذا يضمن عدم وجود تضارب في الربط

### 2. **السماح بالدفعات المتعددة**
- النظام يسمح بدفعات متعددة لنفس الطالب في نفس الشهر
- كل دفعة تحصل على معاملة بنكية منفصلة
- عند التعديل، الدفعة الجديدة تأخذ ارتباط المعاملة البنكية للدفعة القديمة

### 3. **الأمان**
- جميع العمليات تتم داخل معاملات قاعدة البيانات
- في حالة فشل أي خطوة، يتم التراجع عن كامل العملية

## 📊 مراقبة النظام

### استعلامات مفيدة للمراقبة:

```sql
-- التحقق من المعاملات البنكية غير المرتبطة
SELECT * FROM bank_transactions
WHERE financial_transaction_log_id IS NULL;

-- التحقق من السجلات المالية بدون معاملات بنكية
SELECT ftl.* FROM financial_transactions_log ftl
LEFT JOIN bank_transactions bt ON bt.financial_transaction_log_id = ftl.id
WHERE bt.id IS NULL AND ftl.is_reversal = false;

-- عرض تاريخ دفعة طالب معين
SELECT * FROM financial_transactions_log
WHERE source_record_id = 'payment_id'
ORDER BY created_at;

-- عرض تاريخ مصروف سائق معين
SELECT * FROM financial_transactions_log
WHERE source_record_id = 'driver_expense_id' AND source_table = 'driver_expenses'
ORDER BY created_at;

-- التحقق من مصروفات السائقين غير المدفوعة (لا يجب أن تظهر في السجل المالي)
SELECT de.id, de.expense_type, de.total_amount, de.bank_paid_amount
FROM driver_expenses de
LEFT JOIN financial_transactions_log ftl ON ftl.source_record_id = de.id::text AND ftl.source_table = 'driver_expenses'
WHERE de.bank_paid_amount = 0 AND ftl.id IS NOT NULL;

-- التحقق من توازن دفعة طالب
WITH payment_history AS (
    SELECT
        source_record_id,
        SUM(CASE WHEN is_reversal = false THEN amount ELSE -amount END) as net_amount
    FROM financial_transactions_log
    WHERE source_record_id = 'payment_id'
    GROUP BY source_record_id
)
SELECT sp.amount as original_amount, ph.net_amount, bt.amount as bank_amount
FROM student_payments sp
JOIN payment_history ph ON ph.source_record_id = sp.id::text
LEFT JOIN bank_transactions bt ON bt.reference_id = sp.id
WHERE sp.id = 'payment_id';
```

## 🔧 الصيانة

### تنظيف دوري:
```sql
-- حذف المعاملات البنكية غير المرتبطة
DELETE FROM bank_transactions
WHERE financial_transaction_log_id IS NULL;

-- التحقق من السجلات المعلقة
SELECT 'Orphaned bank transactions' as issue, COUNT(*) as count
FROM bank_transactions
WHERE financial_transaction_log_id IS NULL
UNION ALL
SELECT 'Financial logs without bank transactions' as issue, COUNT(*) as count
FROM financial_transactions_log ftl
LEFT JOIN bank_transactions bt ON bt.financial_transaction_log_id = ftl.id
WHERE bt.id IS NULL AND ftl.is_reversal = false;
```

### التحقق من التوازن:
```sql
-- التأكد من أن كل دفعة لها معاملة بنكية
SELECT sp.id, sp.amount, bt.amount as bank_amount,
       CASE WHEN bt.id IS NULL THEN 'Missing bank transaction'
            WHEN sp.amount != bt.amount THEN 'Amount mismatch'
            ELSE 'OK' END as status
FROM student_payments sp
LEFT JOIN bank_transactions bt ON bt.reference_id = sp.id
WHERE bt.id IS NULL OR sp.amount != bt.amount;

-- التحقق من مصروفات السائقين (حالة خاصة)
SELECT de.id, de.total_amount, de.bank_paid_amount, bt.amount as bank_amount,
       CASE
           WHEN de.bank_paid_amount = 0 OR de.bank_paid_amount IS NULL THEN 'Not paid - OK (no bank transaction expected)'
           WHEN bt.id IS NULL THEN 'Missing bank transaction'
           WHEN de.bank_paid_amount != bt.amount THEN 'Amount mismatch'
           ELSE 'OK'
       END as status
FROM driver_expenses de
LEFT JOIN bank_transactions bt ON bt.reference_table = 'driver_expenses' AND bt.reference_id::text = de.id::text
WHERE (de.bank_paid_amount > 0 AND (bt.id IS NULL OR de.bank_paid_amount != bt.amount))
   OR (de.bank_paid_amount = 0 AND bt.id IS NOT NULL);
```

## 🔍 الاختلافات الخاصة بكل جدول

### 1. `student_payments` - دفعات الطلاب
```sql
-- المنطق الأساسي
- يستخدم عمود amount مباشرة
- نوع المعاملة: إيداع دائماً
- يسمح بدفعات متعددة لنفس الطالب في نفس الشهر
- معرف UUID
```

### 2. `driver_expenses` - مصروفات السائقين ⚠️ **حالة خاصة**
```sql
-- المنطق الخاص
- يستخدم عمود bank_paid_amount (وليس total_amount)
- شرط التشغيل: bank_paid_amount > 0
- إذا كان bank_paid_amount = 0 أو NULL → لا يعمل التريجر
- نوع المعاملة يعتمد على is_deduction:
  * is_deduction = true → سحب (خصم من راتب)
  * is_deduction = false → مصروفات (مصروف عادي)
- معرف bigint (وليس UUID)
- السجلات العكسية تستخدم النوع المعاكس:
  * عكس سحب → إيداع
  * عكس مصروفات → استرداد
```

**مثال driver_expenses:**
```sql
-- مصروف وقود: total_amount = 1000, bank_paid_amount = 750
-- النتيجة: يرسل 750 ريال كـ "مصروفات"

-- خصم من راتب: total_amount = 500, bank_paid_amount = 500, is_deduction = true
-- النتيجة: يرسل 500 ريال كـ "سحب"

-- مصروف غير مدفوع: total_amount = 300, bank_paid_amount = 0
-- النتيجة: لا يرسل شيء (التريجر لا يعمل)
```

### 3. `bus_expenses` - مصروفات الحافلات
```sql
-- المنطق الأساسي
- يستخدم total_with_tax إذا كان موجود، وإلا amount
- نوع المعاملة: مصروفات دائماً
- يدعم الضرائب تلقائياً
- معرف UUID
```

### 4. `nathriyat_transactions` - معاملات النظريات
```sql
-- المنطق الأساسي
- يستخدم عمود amount مباشرة
- نوع المعاملة: مصروفات دائماً
- معرف UUID
```

## 📊 ملخص أنواع المعاملات

| الجدول | نوع المعاملة | العمود المستخدم | شروط خاصة |
|--------|-------------|----------------|------------|
| `student_payments` | `إيداع` | `amount` | - |
| `driver_expenses` | `سحب` أو `مصروفات` | `bank_paid_amount` | `bank_paid_amount > 0` |
| `bus_expenses` | `مصروفات` | `total_with_tax` أو `amount` | - |
| `nathriyat_transactions` | `مصروفات` | `amount` | - |

## 🎯 الخلاصة

هذا النظام يوفر:

1. **تتبع كامل** لجميع التغييرات المالية
2. **شفافية مطلقة** في السجلات المالية
3. **ربط ذكي** بين الجداول المختلفة
4. **دعم الدفعات المتعددة** لنفس الطالب في نفس الشهر
5. **تنظيف تلقائي** للبيانات المعلقة
6. **أمان عالي** ضد فقدان البيانات
7. **مرونة في التعديل** مع الحفاظ على الارتباط الصحيح

النظام مصمم ليكون **قابل للتدقيق** و **موثوق** و **قابل للصيانة** مع الحفاظ على **الأداء العالي** و **المرونة في الاستخدام**.

## 🆕 الدوال الجديدة للأوصاف التفصيلية

### 1. دالة الحصول على التكلفة الشهرية للطالب
**اسم الدالة:** `get_student_monthly_cost(p_student_id uuid, p_budget_month_id uuid)`

#### الوظيفة:
تبحث عن التكلفة الشهرية للطالب بالترتيب التالي:
1. **أولاً**: البحث في جدول `student_month_costs` للطالب والشهر المحدد
2. **ثانياً**: إذا لم توجد، البحث في جدول `student_subscription_defaults` للطالب
3. **أخيراً**: إرجاع 0 إذا لم توجد أي تكلفة

#### مثال الاستخدام:
```sql
SELECT get_student_monthly_cost('student_id', 'month_id') as monthly_cost;
```

### 2. دالة توليد الوصف التفصيلي لدفعات الطلاب
**اسم الدالة:** `generate_student_payment_description(p_student_payment_id uuid)`

#### المعلومات المتضمنة في الوصف:
- **اسم الطالب**: من جدول `students`
- **رقم الدفعة**: عدد الدفعات السابقة للطالب في نفس الشهر + 1
- **الشهر والسنة**: من جداول `budget_months` و `budget_years`
- **المبلغ المتبقي**: التكلفة الشهرية - إجمالي المدفوع حتى هذه الدفعة
- **حالة السداد**:
  - "مسدد بالكامل" إذا كان المتبقي ≤ 0
  - "دفع جزئي" إذا كان المتبقي > 0
- **التكلفة الشهرية**: من دالة `get_student_monthly_cost()`

#### مثال الوصف الناتج:
```
دفعة طالب: روان راشد - دفعة #1 لشهر مارس/2026 - المتبقي: 150.00 ريال - الحالة: دفع جزئي - التكلفة الشهرية: 350.00 ريال
```

### 3. دالة توليد الوصف التفصيلي للدفعات المؤجلة
**اسم الدالة:** `generate_deferred_payment_description(p_deferred_payment_id uuid)`

#### المعلومات المتضمنة في الوصف:
- **رقم الدفعة**: للمعاملة المؤجلة
- **اسم البنك الآجل**: من جدول `banks`
- **تفاصيل المعاملة الأصلية**: من جدول `bank_transactions`
  - إذا لم توجد تفاصيل: "سُجلت من دون تفاصيل"
- **المبلغ المتبقي**: المبلغ الأصلي - إجمالي المدفوع

#### مثال الوصف الناتج:
```
دفع مستحق #2 - بنك آجل تجريبي - إيداع تجريبي للاختبار - المتبقي: 79.00 ريال
```

### 4. التحديثات على التريجرات الموجودة

#### تريجر دفعات الطلاب:
- **تم تحديث** دالة `handle_student_payment_changes_v2()` لاستخدام `generate_student_payment_description()`
- **النتيجة**: أوصاف تفصيلية باللغة العربية بدلاً من الأوصاف البسيطة

#### تريجر الدفعات المؤجلة:
- **تم تحديث** دالة `handle_deferred_payment_changes()` لاستخدام `generate_deferred_payment_description()`
- **النتيجة**: أوصاف تفصيلية تتضمن جميع المعلومات المطلوبة

### 5. الفوائد الجديدة:

1. **وضوح أكبر**: الأوصاف تحتوي على معلومات مفصلة ومفهومة
2. **سهولة التتبع**: يمكن معرفة تفاصيل كل دفعة من الوصف مباشرة
3. **دعم اللغة العربية**: جميع الأوصاف باللغة العربية
4. **معلومات شاملة**: تتضمن الحالة المالية والمبالغ المتبقية
5. **مرونة في التكلفة**: دعم التكلفة المخصصة والافتراضية للطلاب

### 6. دالة توليد الوصف التفصيلي لمصروفات الباصات
**اسم الدالة:** `generate_bus_expense_description(p_bus_expense_id uuid)`

#### المعلومات المتضمنة في الوصف:
- **رقم الباص**: من جدول `buses`
- **رقم المصروف**: عدد المصروفات السابقة للباص في نفس الشهر + 1
- **الشهر والسنة**: من جداول `budget_months` و `budget_years`
- **نوع المصروف**: من حقل `expense_type`
- **الوصف التفصيلي**: من حقل `details`
  - إذا لم يوجد وصف: "بدون تفاصيل إضافية"

#### مثال الوصف الناتج:
```
مصروف باص رقم: 22 - مصروف #1 لشهر مارس/2026 - نوع المصروف: إطارات - الوصف: تغيير إطارين أماميين
```

#### مثال بدون تفاصيل:
```
مصروف باص رقم: 22 - مصروف #6 لشهر فبراير/2026 - نوع المصروف: صيانة - بدون تفاصيل إضافية
```

### 7. التحديثات على تريجر مصروفات الباصات

#### تريجر مصروفات الباصات:
- **تم تحديث** دالة `handle_bus_expenses_changes()` لاستخدام `generate_bus_expense_description()`
- **النتيجة**: أوصاف تفصيلية تتضمن رقم الباص ونوع المصروف والتفاصيل
- **الميزة الخاصة**: يدعم المبلغ مع الضريبة (`total_with_tax`) تلقائياً

### 8. الفوائد الإضافية الجديدة:

1. **تتبع مصروفات الباصات**: معرفة أي باص وأي نوع مصروف من الوصف مباشرة
2. **ترقيم المصروفات**: رقم تسلسلي لكل مصروف للباص في الشهر الواحد
3. **شمولية المعلومات**: تفاصيل كاملة عن المصروف في وصف واحد
4. **مرونة في التفاصيل**: دعم المصروفات مع وبدون تفاصيل إضافية
