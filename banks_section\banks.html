<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة البنوك</title>
    <!-- Shared Styles -->
    <link rel="stylesheet" href="../shared_styles.css"> <!-- Adjusted path -->
    <!-- Page Specific Styles -->
    <link rel="stylesheet" href="style.css">
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Config -->
    <script src="../config.js"></script> <!-- Adjusted path -->
    <!-- Auth Script -->
    <script src="../auth.js"></script> <!-- Adjusted path -->
    <!-- <PERSON> Script -->
    <script defer src="script.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts (Example: Tajawal) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navbar (Shared Structure) -->
    <nav class="main-navbar">
        <div class="navbar-brand">
            <i class="fas fa-university"></i> <!-- Updated Icon -->
            إدارة البنوك
        </div>
        <div class="navbar-user">
            <span id="navbar-username"></span>
            <button id="logout-btn" class="logout-btn" style="display: none;">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </nav>

    <!-- Main Container with Sidebar (Shared Structure) -->
    <div class="container with-sidebar"> <!-- Added 'with-sidebar' class -->

        <!-- Sidebar (Shared Structure) -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3>القائمة</h3>
            </div>
            <ul class="sidebar-list">
                <!-- Static links if needed -->
                 <li><a href="../financial_section/financial_dashboard.html"><i class="fas fa-tachometer-alt"></i> لوحة المعلومات</a></li>
                 <!-- Add other relevant links if necessary -->
            </ul>
            <div class="sidebar-footer">
                 <button id="back-to-finance-btn-sidebar" class="sidebar-btn">
                     <i class="fas fa-arrow-left"></i> العودة للوحة المعلومات
                 </button>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="dashboard-main"> <!-- Use standard main content class -->
            <!-- Page Header -->
            <header class="dashboard-page-header">
                <h1><i class="fas fa-university"></i> إدارة البنوك</h1>
                <p>إضافة وتعديل وحذف حسابات البنوك.</p>
                <div id="dashboard-message" class="message" style="display: none;"></div>
            </header>

            <!-- Add/Edit Bank Form Section -->
            <section class="form-section-inline"> <!-- Use a specific class if needed -->
                <div class="form-card">
                    <div class="card-header">
                        <h2 id="form-title"><i class="fas fa-plus-circle"></i> إضافة بنك جديد</h2>
                    </div>
                    <div class="card-body">
                        <form id="bank-form">
                            <input type="hidden" id="bank_id" name="bank_id">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="bank_name">اسم البنك <span class="required">*</span></label> <!-- Changed id/name to bank_name -->
                                    <input type="text" id="bank_name" name="bank_name" required placeholder="اسم البنك">
                                </div>
                                <div class="form-group">
                                    <label for="account_number">رقم الحساب <span class="required">*</span></label> <!-- Added required span based on image -->
                                    <input type="text" id="account_number" name="account_number" required placeholder="رقم الحساب البنكي"> <!-- Added required attribute -->
                                </div>
                                <div class="form-group">
                                    <label for="iban">رقم الآيبان</label>
                                    <input type="text" id="iban" name="iban" placeholder="SA..."> <!-- Added IBAN field -->
                                </div>
                                <div class="form-group">
                                    <label for="bank_type">نوع البنك</label> <!-- Added Bank Type field -->
                                    <select id="bank_type" name="bank_type">
                                        <option value="" disabled selected>اختر نوع البنك</option>
                                        <option value="مركزي">مركزي</option>
                                        <option value="اجل">آجل</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="contact_phone">هاتف الاتصال</label> <!-- Added Contact Phone field -->
                                    <input type="tel" id="contact_phone" name="contact_phone" placeholder="رقم الهاتف">
                                </div>
                                <div class="form-group form-group-full-width"> <!-- Example for a full-width field -->
                                     <label for="notes">ملاحظات</label> <!-- Added Notes field -->
                                     <textarea id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية"></textarea>
                                </div>
                                <!-- Removed Initial Balance field -->
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="submit-btn"><i class="fas fa-save"></i> حفظ البنك</button>
                                <button type="button" id="cancel-edit-btn" class="cancel-btn" style="display: none;"><i class="fas fa-times"></i> إلغاء التعديل</button>
                            </div>
                        </form>
                        <div id="form-message" class="message"></div>
                    </div>
                </div>
            </section>

            <!-- Action Buttons Section -->
            <section class="action-buttons-section">
                <div class="action-buttons-card">
                    <div class="action-buttons-grid">
                        <button id="pay-due-btn" class="action-btn pay-due-btn">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>دفع المستحق</span>
                        </button>
                        <button id="add-bank-btn" class="action-btn add-bank-btn">
                            <i class="fas fa-plus-circle"></i>
                            <span>إضافة بنك جديد</span>
                        </button>
                        <button id="central-banks-btn" class="action-btn central-banks-btn">
                            <i class="fas fa-university"></i>
                            <span>البنوك المركزية</span>
                        </button>
                        <button id="review-transactions-btn" class="action-btn review-btn">
                            <i class="fas fa-arrow-left"></i>
                            <span>مراجعة المعاملات</span>
                        </button>
                    </div>
                </div>
            </section>

            <!-- Banks Table Section -->
            <section class="table-section">
                <div class="table-card">
                    <div class="card-header">
                        <h2><i class="fas fa-list"></i> قائمة البنوك</h2>
                        <span class="badge" id="banks-count">0</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="banks-table">
                                <thead>
                                    <tr>
                                        <th>اسم البنك</th>
                                        <th>رقم الحساب</th>
                                        <th>الآيبان</th> <!-- Added IBAN Header -->
                                        <th>نوع البنك</th> <!-- Added Bank Type Header -->
                                        <th>هاتف الاتصال</th> <!-- Added Contact Phone Header -->
                                        <th>ملاحظات</th> <!-- Added Notes Header -->
                                        <th>إجراءات</th>
                                        <!-- Removed Initial Balance and Current Balance Headers -->
                                    </tr>
                                </thead>
                                <tbody id="banks-tbody">
                                    <!-- Rows loaded by JS -->
                                    <!-- Updated colspan to 7 -->
                                    <tr><td colspan="7" class="loading-message">جاري تحميل البنوك...</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div id="list-message" class="message"></div>
                    </div>
                </div>
            </section>

        </main> <!-- Close dashboard-main -->
    </div> <!-- Close container -->

    <!-- Pay Due Modal -->
    <div id="pay-due-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-money-bill-wave"></i> دفع المستحق</h3>
                <button class="close-btn" id="close-pay-due-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="step-container">
                    <!-- Step 1: Select Deferred Bank -->
                    <div id="step-1" class="step active">
                        <h4>اختر البنك الآجل:</h4>
                        <div id="deferred-banks-list" class="banks-list">
                            <!-- Banks will be loaded here -->
                        </div>
                    </div>

                    <!-- Step 2: Select Deposit Transaction -->
                    <div id="step-2" class="step">
                        <div class="step-header">
                            <button id="back-to-step-1" class="back-btn">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                            <h4 id="selected-bank-name">معاملات الإيداع</h4>
                        </div>
                        <div id="deposit-transactions-list" class="transactions-list">
                            <!-- Transactions will be loaded here -->
                        </div>
                    </div>

                    <!-- Step 3: Payment Form -->
                    <div id="step-3" class="step">
                        <div class="step-header">
                            <button id="back-to-step-2" class="back-btn">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                            <h4>تفاصيل الدفع</h4>
                        </div>
                        <form id="payment-form">
                            <div class="form-group">
                                <label for="central-bank-select">البنك المركزي:</label>
                                <select id="central-bank-select" required>
                                    <option value="">اختر البنك المركزي</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="payment-date">تاريخ الدفع:</label>
                                <input type="date" id="payment-date" required>
                            </div>
                            <div class="form-group">
                                <label for="payment-details">التفاصيل:</label>
                                <textarea id="payment-details" rows="3" placeholder="تفاصيل إضافية عن الدفع"></textarea>
                            </div>
                            <div class="payment-summary">
                                <div class="summary-item">
                                    <span>المبلغ المستحق:</span>
                                    <span id="due-amount">0</span>
                                </div>
                                <div class="summary-item">
                                    <span>البنك الآجل:</span>
                                    <span id="deferred-bank-name">-</span>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="submit-btn">
                                    <i class="fas fa-check"></i> تأكيد الدفع
                                </button>
                                <button type="button" id="cancel-payment" class="cancel-btn">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div id="pay-due-message" class="message"></div>
            </div>
        </div>
    </div>

    <!-- Footer (Optional) -->
    <!--
    <footer class="dashboard-footer">
        <p>© 2023 نظام الإدارة المالية</p>
    </footer>
    -->
</body>
</html>
